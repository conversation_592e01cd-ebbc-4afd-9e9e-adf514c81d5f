/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as VerifyEmailImport } from './routes/verify-email'
import { Route as ForgotPasswordCodeImport } from './routes/forgot-password-code'
import { Route as ForgotPasswordImport } from './routes/forgot-password'
import { Route as SignedinImport } from './routes/_signedin'
import { Route as IndexImport } from './routes/index'
import { Route as ImportIndexImport } from './routes/import/index'
import { Route as SignedinAdminImport } from './routes/_signedin/admin'
import { Route as SignedinAdminDashboardImport } from './routes/_signedin/admin/dashboard'
import { Route as ImportStoreIdIndexImport } from './routes/import/store/$id/index'
import { Route as SignedinAdminUsersIndexImport } from './routes/_signedin/admin/users/index'
import { Route as SignedinAdminStoresIndexImport } from './routes/_signedin/admin/stores/index'
import { Route as SignedinAdminPeopleIndexImport } from './routes/_signedin/admin/people/index'
import { Route as SignedinAdminOnboardingIndexImport } from './routes/_signedin/admin/onboarding/index'
import { Route as SignedinAdminNewspaperIndexImport } from './routes/_signedin/admin/newspaper/index'
import { Route as SignedinAdminInvitationsIndexImport } from './routes/_signedin/admin/invitations/index'
import { Route as SignedinAdminFeaturesIndexImport } from './routes/_signedin/admin/features/index'
import { Route as SignedinAdminEducationIndexImport } from './routes/_signedin/admin/education/index'
import { Route as SignedinAdminBusinessesIndexImport } from './routes/_signedin/admin/businesses/index'
import { Route as SignedinBusinessIdStoreIdIndexImport } from './routes/_signedin/$businessId/$storeId/index'
import { Route as SignedinAdminUsersCreateImport } from './routes/_signedin/admin/users/create'
import { Route as SignedinAdminNewspaperVisibilityImport } from './routes/_signedin/admin/newspaper/visibility'
import { Route as SignedinAdminInvitationsCreateImport } from './routes/_signedin/admin/invitations/create'
import { Route as SignedinAdminEducationCreateImport } from './routes/_signedin/admin/education/create'
import { Route as SignedinAdminEducationIdImport } from './routes/_signedin/admin/education/$id'
import { Route as SignedinAdminBusinessesCreateImport } from './routes/_signedin/admin/businesses/create'
import { Route as SignedinBusinessIdStoreIdNavImport } from './routes/_signedin/$businessId/$storeId/_nav'
import { Route as SignedinAdminBusinessesBusinessIdIndexImport } from './routes/_signedin/admin/businesses/$businessId/index'
import { Route as SignedinBusinessIdStoreIdOnboardingIndexImport } from './routes/_signedin/$businessId/$storeId/onboarding/index'
import { Route as SignedinAdminUsersUserIdEditImport } from './routes/_signedin/admin/users/$userId.edit'
import { Route as SignedinAdminPeoplePersonIdStreamchatImport } from './routes/_signedin/admin/people/$personId.streamchat'
import { Route as SignedinAdminPeoplePersonIdEditImport } from './routes/_signedin/admin/people/$personId.edit'
import { Route as SignedinAdminBusinessesBusinessIdInviteImport } from './routes/_signedin/admin/businesses/$businessId/invite'
import { Route as SignedinBusinessIdStoreIdReportsHourlySalesImport } from './routes/_signedin/$businessId/$storeId/reports/hourly-sales'
import { Route as SignedinBusinessIdStoreIdReportsAvailabilityImport } from './routes/_signedin/$businessId/$storeId/reports/availability'
import { Route as SignedinBusinessIdStoreIdOnboardingRequiredBreaksImport } from './routes/_signedin/$businessId/$storeId/onboarding/requiredBreaks'
import { Route as SignedinBusinessIdStoreIdOnboardingLaborLawsImport } from './routes/_signedin/$businessId/$storeId/onboarding/laborLaws'
import { Route as SignedinBusinessIdStoreIdOnboardingCompleteImport } from './routes/_signedin/$businessId/$storeId/onboarding/complete'
import { Route as SignedinBusinessIdStoreIdNavWalkthroughImport } from './routes/_signedin/$businessId/$storeId/_nav/walkthrough'
import { Route as SignedinBusinessIdStoreIdNavTrainingImport } from './routes/_signedin/$businessId/$storeId/_nav/training'
import { Route as SignedinBusinessIdStoreIdNavTimePunchImport } from './routes/_signedin/$businessId/$storeId/_nav/timePunch'
import { Route as SignedinBusinessIdStoreIdNavTeamImport } from './routes/_signedin/$businessId/$storeId/_nav/team'
import { Route as SignedinBusinessIdStoreIdNavStoreImport } from './routes/_signedin/$businessId/$storeId/_nav/store'
import { Route as SignedinBusinessIdStoreIdNavSettingsImport } from './routes/_signedin/$businessId/$storeId/_nav/settings'
import { Route as SignedinBusinessIdStoreIdNavPositionsImport } from './routes/_signedin/$businessId/$storeId/_nav/positions'
import { Route as SignedinBusinessIdStoreIdNavHrImport } from './routes/_signedin/$businessId/$storeId/_nav/hr'
import { Route as SignedinBusinessIdStoreIdNavEducationImport } from './routes/_signedin/$businessId/$storeId/_nav/education'
import { Route as SignedinBusinessIdStoreIdSchedulesScheduleIdIndexImport } from './routes/_signedin/$businessId/$storeId/schedules/$scheduleId/index'
import { Route as SignedinBusinessIdStoreIdNavWalkthroughIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/walkthrough/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/index'
import { Route as SignedinBusinessIdStoreIdNavEducationIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/education/index'
import { Route as SignedinBusinessIdStoreIdNavDevicesIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/devices/index'
import { Route as SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditImport } from './routes/_signedin/admin/newspaper/visibilityGroup/$visibilityGroupId/edit'
import { Route as SignedinAdminNewspaperPaperNewspaperIdEditImport } from './routes/_signedin/admin/newspaper/paper/$newspaperId/edit'
import { Route as SignedinAdminBusinessesBusinessIdStoresCreateImport } from './routes/_signedin/admin/businesses/$businessId/stores/create'
import { Route as SignedinBusinessIdStoreIdPrintScheduleIdTeamImport } from './routes/_signedin/$businessId/$storeId/print/$scheduleId/team'
import { Route as SignedinBusinessIdStoreIdPrintScheduleIdDailyImport } from './routes/_signedin/$businessId/$storeId/print/$scheduleId/daily'
import { Route as SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadImport } from './routes/_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload'
import { Route as SignedinBusinessIdStoreIdNavWalkthroughStepImport } from './routes/_signedin/$businessId/$storeId/_nav/walkthrough/$step'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings'
import { Route as SignedinBusinessIdStoreIdNavSchedulesSettingsImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/settings'
import { Route as SignedinBusinessIdStoreIdNavSchedulesInsightsImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/insights'
import { Route as SignedinBusinessIdStoreIdNavSchedulesBuilderImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/builder'
import { Route as SignedinBusinessIdStoreIdNavEducationIdImport } from './routes/_signedin/$businessId/$storeId/_nav/education/$id'
import { Route as SignedinBusinessIdStoreIdNavDevicesClientIdImport } from './routes/_signedin/$businessId/$storeId/_nav/devices/$clientId'
import { Route as SignedinBusinessIdStoreIdNavChecklistsNavImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/_nav'
import { Route as SignedinAdminBusinessesBusinessIdStoresStoreIdIndexImport } from './routes/_signedin/admin/businesses/$businessId/stores/$storeId/index'
import { Route as SignedinBusinessIdStoreIdNavTrainingInsightsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/training/insights/index'
import { Route as SignedinBusinessIdStoreIdNavTimePunchVarianceIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/timePunch/variance/index'
import { Route as SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/timePunch/teamTotals/index'
import { Route as SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/index'
import { Route as SignedinBusinessIdStoreIdNavTimePunchPunchesIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/timePunch/punches/index'
import { Route as SignedinBusinessIdStoreIdNavTeamReportsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/team/reports/index'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/index'
import { Route as SignedinBusinessIdStoreIdNavStoreLinksIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/store/links/index'
import { Route as SignedinBusinessIdStoreIdNavStoreFilesIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/store/files/index'
import { Route as SignedinBusinessIdStoreIdNavStoreEventsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/store/events/index'
import { Route as SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/store/announcements/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesSettingsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/settings/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesReportsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/reports/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesDataIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/data/index'
import { Route as SignedinBusinessIdStoreIdNavHrRemindersIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/hr/reminders/index'
import { Route as SignedinBusinessIdStoreIdNavHrInsightsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/hr/insights/index'
import { Route as SignedinBusinessIdStoreIdNavHrDocumentsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/hr/documents/index'
import { Route as SignedinBusinessIdStoreIdNavChecklistsNavIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/_nav/index'
import { Route as SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/index'
import { Route as SignedinBusinessIdStoreIdPrintInsightsSchedulingTableImport } from './routes/_signedin/$businessId/$storeId/print/insights/scheduling/table'
import { Route as SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsImport } from './routes/_signedin/$businessId/$storeId/print/insights/scheduling/shifts'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsWorkModeImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/work-mode'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/store-info'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/core-values'
import { Route as SignedinBusinessIdStoreIdNavStoreLinksLinkImport } from './routes/_signedin/$businessId/$storeId/_nav/store/links/$link'
import { Route as SignedinBusinessIdStoreIdNavStoreAnnouncementsViewImport } from './routes/_signedin/$businessId/$storeId/_nav/store/announcements/view'
import { Route as SignedinBusinessIdStoreIdNavStoreAnnouncementsEditImport } from './routes/_signedin/$businessId/$storeId/_nav/store/announcements/edit'
import { Route as SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/settings/validations'
import { Route as SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/settings/time-off'
import { Route as SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/settings/labor-laws'
import { Route as SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/settings/break-rules'
import { Route as SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/settings/availability'
import { Route as SignedinBusinessIdStoreIdNavSchedulesDataUploadImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/data/upload'
import { Route as SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/data/$dataFileId'
import { Route as SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/$scheduleId/metrics'
import { Route as SignedinBusinessIdStoreIdNavHrRemindersViewImport } from './routes/_signedin/$businessId/$storeId/_nav/hr/reminders/view'
import { Route as SignedinBusinessIdStoreIdNavHrRemindersEditImport } from './routes/_signedin/$businessId/$storeId/_nav/hr/reminders/edit'
import { Route as SignedinBusinessIdStoreIdNavHrDocumentsViewImport } from './routes/_signedin/$businessId/$storeId/_nav/hr/documents/view'
import { Route as SignedinBusinessIdStoreIdNavHrDocumentsEditImport } from './routes/_signedin/$businessId/$storeId/_nav/hr/documents/edit'
import { Route as SignedinBusinessIdStoreIdNavChecklistsNavUpcomingImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/_nav/upcoming'
import { Route as SignedinBusinessIdStoreIdNavChecklistsNavTemplatesImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/_nav/templates'
import { Route as SignedinBusinessIdStoreIdNavChecklistsNavRecordedImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded'
import { Route as SignedinBusinessIdStoreIdNavChecklistsNavActiveImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/_nav/active'
import { Route as SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view'
import { Route as SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexImport } from './routes/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/index'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/data/files/index'
import { Route as SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/index'
import { Route as SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdImport } from './routes/_signedin/admin/businesses/$businessId/stores/$storeId/person/$personId'
import { Route as SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportImport } from './routes/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/import'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/positions'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/documents'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/area/work-areas'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId'
import { Route as SignedinBusinessIdStoreIdNavStoreFilesFileIdViewImport } from './routes/_signedin/$businessId/$storeId/_nav/store/files/$fileId/view'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/$timeOffId'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/$shiftOfferId'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/$personAvailabilityId'
import { Route as SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/$forecastJobId'
import { Route as SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use'
import { Route as SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/index'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/index'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/wages'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/permissions'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/info'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/personal'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/contact-details'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/$jobId/edit'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/positions'
import { Route as SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditImport } from './routes/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/edit'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/edit/$timeOffId'
import { Route as SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdImport } from './routes/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/$personAvailabilityId'
import { Route as SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdImport } from './routes/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/edit'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/$noteId'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/cmFromAi/$actionableItemId'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view'
import { Route as SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeImport } from './routes/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize'

// Create Virtual Routes

const SignedinBusinessIdStoreIdImport = createFileRoute(
  '/_signedin/$businessId/$storeId',
)()
const SignedinBusinessIdStoreIdNavChecklistsImport = createFileRoute(
  '/_signedin/$businessId/$storeId/_nav/checklists',
)()

// Create/Update Routes

const VerifyEmailRoute = VerifyEmailImport.update({
  id: '/verify-email',
  path: '/verify-email',
  getParentRoute: () => rootRoute,
} as any)

const ForgotPasswordCodeRoute = ForgotPasswordCodeImport.update({
  id: '/forgot-password-code',
  path: '/forgot-password-code',
  getParentRoute: () => rootRoute,
} as any)

const ForgotPasswordRoute = ForgotPasswordImport.update({
  id: '/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRoute,
} as any)

const SignedinRoute = SignedinImport.update({
  id: '/_signedin',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const ImportIndexRoute = ImportIndexImport.update({
  id: '/import/',
  path: '/import/',
  getParentRoute: () => rootRoute,
} as any)

const SignedinAdminRoute = SignedinAdminImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => SignedinRoute,
} as any)

const SignedinBusinessIdStoreIdRoute = SignedinBusinessIdStoreIdImport.update({
  id: '/$businessId/$storeId',
  path: '/$businessId/$storeId',
  getParentRoute: () => SignedinRoute,
} as any)

const SignedinAdminDashboardRoute = SignedinAdminDashboardImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => SignedinAdminRoute,
} as any)

const ImportStoreIdIndexRoute = ImportStoreIdIndexImport.update({
  id: '/import/store/$id/',
  path: '/import/store/$id/',
  getParentRoute: () => rootRoute,
} as any)

const SignedinAdminUsersIndexRoute = SignedinAdminUsersIndexImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => SignedinAdminRoute,
} as any)

const SignedinAdminStoresIndexRoute = SignedinAdminStoresIndexImport.update({
  id: '/stores/',
  path: '/stores/',
  getParentRoute: () => SignedinAdminRoute,
} as any)

const SignedinAdminPeopleIndexRoute = SignedinAdminPeopleIndexImport.update({
  id: '/people/',
  path: '/people/',
  getParentRoute: () => SignedinAdminRoute,
} as any)

const SignedinAdminOnboardingIndexRoute =
  SignedinAdminOnboardingIndexImport.update({
    id: '/onboarding/',
    path: '/onboarding/',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminNewspaperIndexRoute =
  SignedinAdminNewspaperIndexImport.update({
    id: '/newspaper/',
    path: '/newspaper/',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminInvitationsIndexRoute =
  SignedinAdminInvitationsIndexImport.update({
    id: '/invitations/',
    path: '/invitations/',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminFeaturesIndexRoute = SignedinAdminFeaturesIndexImport.update(
  {
    id: '/features/',
    path: '/features/',
    getParentRoute: () => SignedinAdminRoute,
  } as any,
)

const SignedinAdminEducationIndexRoute =
  SignedinAdminEducationIndexImport.update({
    id: '/education/',
    path: '/education/',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminBusinessesIndexRoute =
  SignedinAdminBusinessesIndexImport.update({
    id: '/businesses/',
    path: '/businesses/',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinBusinessIdStoreIdIndexRoute =
  SignedinBusinessIdStoreIdIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinAdminUsersCreateRoute = SignedinAdminUsersCreateImport.update({
  id: '/users/create',
  path: '/users/create',
  getParentRoute: () => SignedinAdminRoute,
} as any)

const SignedinAdminNewspaperVisibilityRoute =
  SignedinAdminNewspaperVisibilityImport.update({
    id: '/newspaper/visibility',
    path: '/newspaper/visibility',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminInvitationsCreateRoute =
  SignedinAdminInvitationsCreateImport.update({
    id: '/invitations/create',
    path: '/invitations/create',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminEducationCreateRoute =
  SignedinAdminEducationCreateImport.update({
    id: '/education/create',
    path: '/education/create',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminEducationIdRoute = SignedinAdminEducationIdImport.update({
  id: '/education/$id',
  path: '/education/$id',
  getParentRoute: () => SignedinAdminRoute,
} as any)

const SignedinAdminBusinessesCreateRoute =
  SignedinAdminBusinessesCreateImport.update({
    id: '/businesses/create',
    path: '/businesses/create',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinBusinessIdStoreIdNavRoute =
  SignedinBusinessIdStoreIdNavImport.update({
    id: '/_nav',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsRoute =
  SignedinBusinessIdStoreIdNavChecklistsImport.update({
    id: '/checklists',
    path: '/checklists',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinAdminBusinessesBusinessIdIndexRoute =
  SignedinAdminBusinessesBusinessIdIndexImport.update({
    id: '/businesses/$businessId/',
    path: '/businesses/$businessId/',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinBusinessIdStoreIdOnboardingIndexRoute =
  SignedinBusinessIdStoreIdOnboardingIndexImport.update({
    id: '/onboarding/',
    path: '/onboarding/',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinAdminUsersUserIdEditRoute =
  SignedinAdminUsersUserIdEditImport.update({
    id: '/users/$userId/edit',
    path: '/users/$userId/edit',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminPeoplePersonIdStreamchatRoute =
  SignedinAdminPeoplePersonIdStreamchatImport.update({
    id: '/people/$personId/streamchat',
    path: '/people/$personId/streamchat',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminPeoplePersonIdEditRoute =
  SignedinAdminPeoplePersonIdEditImport.update({
    id: '/people/$personId/edit',
    path: '/people/$personId/edit',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminBusinessesBusinessIdInviteRoute =
  SignedinAdminBusinessesBusinessIdInviteImport.update({
    id: '/businesses/$businessId/invite',
    path: '/businesses/$businessId/invite',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinBusinessIdStoreIdReportsHourlySalesRoute =
  SignedinBusinessIdStoreIdReportsHourlySalesImport.update({
    id: '/reports/hourly-sales',
    path: '/reports/hourly-sales',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdReportsAvailabilityRoute =
  SignedinBusinessIdStoreIdReportsAvailabilityImport.update({
    id: '/reports/availability',
    path: '/reports/availability',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdOnboardingRequiredBreaksRoute =
  SignedinBusinessIdStoreIdOnboardingRequiredBreaksImport.update({
    id: '/onboarding/requiredBreaks',
    path: '/onboarding/requiredBreaks',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdOnboardingLaborLawsRoute =
  SignedinBusinessIdStoreIdOnboardingLaborLawsImport.update({
    id: '/onboarding/laborLaws',
    path: '/onboarding/laborLaws',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdOnboardingCompleteRoute =
  SignedinBusinessIdStoreIdOnboardingCompleteImport.update({
    id: '/onboarding/complete',
    path: '/onboarding/complete',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavWalkthroughRoute =
  SignedinBusinessIdStoreIdNavWalkthroughImport.update({
    id: '/walkthrough',
    path: '/walkthrough',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTrainingRoute =
  SignedinBusinessIdStoreIdNavTrainingImport.update({
    id: '/training',
    path: '/training',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTimePunchRoute =
  SignedinBusinessIdStoreIdNavTimePunchImport.update({
    id: '/timePunch',
    path: '/timePunch',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamRoute =
  SignedinBusinessIdStoreIdNavTeamImport.update({
    id: '/team',
    path: '/team',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreRoute =
  SignedinBusinessIdStoreIdNavStoreImport.update({
    id: '/store',
    path: '/store',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSettingsRoute =
  SignedinBusinessIdStoreIdNavSettingsImport.update({
    id: '/settings',
    path: '/settings',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavPositionsRoute =
  SignedinBusinessIdStoreIdNavPositionsImport.update({
    id: '/positions',
    path: '/positions',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavHrRoute =
  SignedinBusinessIdStoreIdNavHrImport.update({
    id: '/hr',
    path: '/hr',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavEducationRoute =
  SignedinBusinessIdStoreIdNavEducationImport.update({
    id: '/education',
    path: '/education',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdSchedulesScheduleIdIndexRoute =
  SignedinBusinessIdStoreIdSchedulesScheduleIdIndexImport.update({
    id: '/schedules/$scheduleId/',
    path: '/schedules/$scheduleId/',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavWalkthroughIndexRoute =
  SignedinBusinessIdStoreIdNavWalkthroughIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavWalkthroughRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesIndexImport.update({
    id: '/schedules/',
    path: '/schedules/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavEducationIndexRoute =
  SignedinBusinessIdStoreIdNavEducationIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavEducationRoute,
  } as any)

const SignedinBusinessIdStoreIdNavDevicesIndexRoute =
  SignedinBusinessIdStoreIdNavDevicesIndexImport.update({
    id: '/devices/',
    path: '/devices/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditRoute =
  SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditImport.update({
    id: '/newspaper/visibilityGroup/$visibilityGroupId/edit',
    path: '/newspaper/visibilityGroup/$visibilityGroupId/edit',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminNewspaperPaperNewspaperIdEditRoute =
  SignedinAdminNewspaperPaperNewspaperIdEditImport.update({
    id: '/newspaper/paper/$newspaperId/edit',
    path: '/newspaper/paper/$newspaperId/edit',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminBusinessesBusinessIdStoresCreateRoute =
  SignedinAdminBusinessesBusinessIdStoresCreateImport.update({
    id: '/businesses/$businessId/stores/create',
    path: '/businesses/$businessId/stores/create',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinBusinessIdStoreIdPrintScheduleIdTeamRoute =
  SignedinBusinessIdStoreIdPrintScheduleIdTeamImport.update({
    id: '/print/$scheduleId/team',
    path: '/print/$scheduleId/team',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdPrintScheduleIdDailyRoute =
  SignedinBusinessIdStoreIdPrintScheduleIdDailyImport.update({
    id: '/print/$scheduleId/daily',
    path: '/print/$scheduleId/daily',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadRoute =
  SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadImport.update({
    id: '/team-member-upload',
    path: '/team-member-upload',
    getParentRoute: () => SignedinBusinessIdStoreIdNavWalkthroughRoute,
  } as any)

const SignedinBusinessIdStoreIdNavWalkthroughStepRoute =
  SignedinBusinessIdStoreIdNavWalkthroughStepImport.update({
    id: '/$step',
    path: '/$step',
    getParentRoute: () => SignedinBusinessIdStoreIdNavWalkthroughRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsImport.update({
    id: '/settings',
    path: '/settings',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesSettingsRoute =
  SignedinBusinessIdStoreIdNavSchedulesSettingsImport.update({
    id: '/schedules/settings',
    path: '/schedules/settings',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesInsightsRoute =
  SignedinBusinessIdStoreIdNavSchedulesInsightsImport.update({
    id: '/schedules/insights',
    path: '/schedules/insights',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesBuilderRoute =
  SignedinBusinessIdStoreIdNavSchedulesBuilderImport.update({
    id: '/schedules/builder',
    path: '/schedules/builder',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavEducationIdRoute =
  SignedinBusinessIdStoreIdNavEducationIdImport.update({
    id: '/$id',
    path: '/$id',
    getParentRoute: () => SignedinBusinessIdStoreIdNavEducationRoute,
  } as any)

const SignedinBusinessIdStoreIdNavDevicesClientIdRoute =
  SignedinBusinessIdStoreIdNavDevicesClientIdImport.update({
    id: '/devices/$clientId',
    path: '/devices/$clientId',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsNavRoute =
  SignedinBusinessIdStoreIdNavChecklistsNavImport.update({
    id: '/_nav',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsRoute,
  } as any)

const SignedinAdminBusinessesBusinessIdStoresStoreIdIndexRoute =
  SignedinAdminBusinessesBusinessIdStoresStoreIdIndexImport.update({
    id: '/businesses/$businessId/stores/$storeId/',
    path: '/businesses/$businessId/stores/$storeId/',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTrainingInsightsIndexRoute =
  SignedinBusinessIdStoreIdNavTrainingInsightsIndexImport.update({
    id: '/insights/',
    path: '/insights/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavTrainingRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTimePunchVarianceIndexRoute =
  SignedinBusinessIdStoreIdNavTimePunchVarianceIndexImport.update({
    id: '/variance/',
    path: '/variance/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavTimePunchRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexRoute =
  SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexImport.update({
    id: '/teamTotals/',
    path: '/teamTotals/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavTimePunchRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexRoute =
  SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexImport.update({
    id: '/teamMemberVariance/',
    path: '/teamMemberVariance/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavTimePunchRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTimePunchPunchesIndexRoute =
  SignedinBusinessIdStoreIdNavTimePunchPunchesIndexImport.update({
    id: '/punches/',
    path: '/punches/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavTimePunchRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamReportsIndexRoute =
  SignedinBusinessIdStoreIdNavTeamReportsIndexImport.update({
    id: '/reports/',
    path: '/reports/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavTeamRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryIndexRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryIndexImport.update({
    id: '/directory/',
    path: '/directory/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavTeamRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreLinksIndexRoute =
  SignedinBusinessIdStoreIdNavStoreLinksIndexImport.update({
    id: '/links/',
    path: '/links/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreFilesIndexRoute =
  SignedinBusinessIdStoreIdNavStoreFilesIndexImport.update({
    id: '/files/',
    path: '/files/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreEventsIndexRoute =
  SignedinBusinessIdStoreIdNavStoreEventsIndexImport.update({
    id: '/events/',
    path: '/events/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexRoute =
  SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexImport.update({
    id: '/announcements/',
    path: '/announcements/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesSettingsIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesSettingsIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavSchedulesSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsIndexImport.update({
    id: '/schedules/requests/',
    path: '/schedules/requests/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesReportsIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesReportsIndexImport.update({
    id: '/schedules/reports/',
    path: '/schedules/reports/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesDataIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesDataIndexImport.update({
    id: '/schedules/data/',
    path: '/schedules/data/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavHrRemindersIndexRoute =
  SignedinBusinessIdStoreIdNavHrRemindersIndexImport.update({
    id: '/reminders/',
    path: '/reminders/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavHrRoute,
  } as any)

const SignedinBusinessIdStoreIdNavHrInsightsIndexRoute =
  SignedinBusinessIdStoreIdNavHrInsightsIndexImport.update({
    id: '/insights/',
    path: '/insights/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavHrRoute,
  } as any)

const SignedinBusinessIdStoreIdNavHrDocumentsIndexRoute =
  SignedinBusinessIdStoreIdNavHrDocumentsIndexImport.update({
    id: '/documents/',
    path: '/documents/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavHrRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsNavIndexRoute =
  SignedinBusinessIdStoreIdNavChecklistsNavIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexRoute =
  SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexImport.update({
    id: '/$checklistId/',
    path: '/$checklistId/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsRoute,
  } as any)

const SignedinBusinessIdStoreIdPrintInsightsSchedulingTableRoute =
  SignedinBusinessIdStoreIdPrintInsightsSchedulingTableImport.update({
    id: '/print/insights/scheduling/table',
    path: '/print/insights/scheduling/table',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsRoute =
  SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsImport.update({
    id: '/print/insights/scheduling/shifts',
    path: '/print/insights/scheduling/shifts',
    getParentRoute: () => SignedinBusinessIdStoreIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdImport.update({
    id: '/directory/$personId',
    path: '/directory/$personId',
    getParentRoute: () => SignedinBusinessIdStoreIdNavTeamRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsWorkModeRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsWorkModeImport.update({
    id: '/work-mode',
    path: '/work-mode',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoImport.update({
    id: '/store-info',
    path: '/store-info',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesImport.update({
    id: '/core-values',
    path: '/core-values',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreLinksLinkRoute =
  SignedinBusinessIdStoreIdNavStoreLinksLinkImport.update({
    id: '/links/$link',
    path: '/links/$link',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreAnnouncementsViewRoute =
  SignedinBusinessIdStoreIdNavStoreAnnouncementsViewImport.update({
    id: '/announcements/view',
    path: '/announcements/view',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreAnnouncementsEditRoute =
  SignedinBusinessIdStoreIdNavStoreAnnouncementsEditImport.update({
    id: '/announcements/edit',
    path: '/announcements/edit',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsRoute =
  SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsImport.update({
    id: '/validations',
    path: '/validations',
    getParentRoute: () => SignedinBusinessIdStoreIdNavSchedulesSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffRoute =
  SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffImport.update({
    id: '/time-off',
    path: '/time-off',
    getParentRoute: () => SignedinBusinessIdStoreIdNavSchedulesSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsRoute =
  SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsImport.update({
    id: '/labor-laws',
    path: '/labor-laws',
    getParentRoute: () => SignedinBusinessIdStoreIdNavSchedulesSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesRoute =
  SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesImport.update({
    id: '/break-rules',
    path: '/break-rules',
    getParentRoute: () => SignedinBusinessIdStoreIdNavSchedulesSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityRoute =
  SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityImport.update({
    id: '/availability',
    path: '/availability',
    getParentRoute: () => SignedinBusinessIdStoreIdNavSchedulesSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesDataUploadRoute =
  SignedinBusinessIdStoreIdNavSchedulesDataUploadImport.update({
    id: '/schedules/data/upload',
    path: '/schedules/data/upload',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdRoute =
  SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdImport.update({
    id: '/schedules/data/$dataFileId',
    path: '/schedules/data/$dataFileId',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsRoute =
  SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsImport.update({
    id: '/schedules/$scheduleId/metrics',
    path: '/schedules/$scheduleId/metrics',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavHrRemindersViewRoute =
  SignedinBusinessIdStoreIdNavHrRemindersViewImport.update({
    id: '/reminders/view',
    path: '/reminders/view',
    getParentRoute: () => SignedinBusinessIdStoreIdNavHrRoute,
  } as any)

const SignedinBusinessIdStoreIdNavHrRemindersEditRoute =
  SignedinBusinessIdStoreIdNavHrRemindersEditImport.update({
    id: '/reminders/edit',
    path: '/reminders/edit',
    getParentRoute: () => SignedinBusinessIdStoreIdNavHrRoute,
  } as any)

const SignedinBusinessIdStoreIdNavHrDocumentsViewRoute =
  SignedinBusinessIdStoreIdNavHrDocumentsViewImport.update({
    id: '/documents/view',
    path: '/documents/view',
    getParentRoute: () => SignedinBusinessIdStoreIdNavHrRoute,
  } as any)

const SignedinBusinessIdStoreIdNavHrDocumentsEditRoute =
  SignedinBusinessIdStoreIdNavHrDocumentsEditImport.update({
    id: '/documents/edit',
    path: '/documents/edit',
    getParentRoute: () => SignedinBusinessIdStoreIdNavHrRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsNavUpcomingRoute =
  SignedinBusinessIdStoreIdNavChecklistsNavUpcomingImport.update({
    id: '/upcoming',
    path: '/upcoming',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsNavTemplatesRoute =
  SignedinBusinessIdStoreIdNavChecklistsNavTemplatesImport.update({
    id: '/templates',
    path: '/templates',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsNavRecordedRoute =
  SignedinBusinessIdStoreIdNavChecklistsNavRecordedImport.update({
    id: '/recorded',
    path: '/recorded',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsNavActiveRoute =
  SignedinBusinessIdStoreIdNavChecklistsNavActiveImport.update({
    id: '/active',
    path: '/active',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewRoute =
  SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewImport.update({
    id: '/$checklistId/view',
    path: '/$checklistId/view',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsRoute,
  } as any)

const SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexRoute =
  SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexImport.update({
    id: '/businesses/$businessId/stores/$storeId/checklists/',
    path: '/businesses/$businessId/stores/$storeId/checklists/',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexImport.update({
    id: '/job-titles/',
    path: '/job-titles/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexImport.update({
    id: '/schedules/requests/timeOff/',
    path: '/schedules/requests/timeOff/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexImport.update({
    id: '/schedules/requests/shiftOffers/',
    path: '/schedules/requests/shiftOffers/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexImport.update({
    id: '/schedules/requests/availability/',
    path: '/schedules/requests/availability/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexImport.update({
    id: '/schedules/data/forecasts/',
    path: '/schedules/data/forecasts/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexImport.update({
    id: '/schedules/data/files/',
    path: '/schedules/data/files/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexRoute =
  SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexImport.update({
    id: '/templates/$templateId/',
    path: '/templates/$templateId/',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsRoute,
  } as any)

const SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdRoute =
  SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdImport.update({
    id: '/businesses/$businessId/stores/$storeId/person/$personId',
    path: '/businesses/$businessId/stores/$storeId/person/$personId',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportRoute =
  SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportImport.update({
    id: '/businesses/$businessId/stores/$storeId/checklists/import',
    path: '/businesses/$businessId/stores/$storeId/checklists/import',
    getParentRoute: () => SignedinAdminRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsImport.update({
    id: '/positions',
    path: '/positions',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport.update({
    id: '/notes',
    path: '/notes',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentImport.update({
    id: '/employment',
    path: '/employment',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsImport.update({
    id: '/documents',
    path: '/documents',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutImport.update({
    id: '/about',
    path: '/about',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasImport.update({
    id: '/area/work-areas',
    path: '/area/work-areas',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdImport.update({
    id: '/area/$areaId',
    path: '/area/$areaId',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreFilesFileIdViewRoute =
  SignedinBusinessIdStoreIdNavStoreFilesFileIdViewImport.update({
    id: '/files/$fileId/view',
    path: '/files/$fileId/view',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateImport.update({
    id: '/schedules/requests/timeOff/create',
    path: '/schedules/requests/timeOff/create',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdImport.update({
    id: '/schedules/requests/timeOff/$timeOffId',
    path: '/schedules/requests/timeOff/$timeOffId',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdImport.update(
    {
      id: '/schedules/requests/shiftOffers/$shiftOfferId',
      path: '/schedules/requests/shiftOffers/$shiftOfferId',
      getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdImport.update(
    {
      id: '/schedules/requests/availability/$personAvailabilityId',
      path: '/schedules/requests/availability/$personAvailabilityId',
      getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdRoute =
  SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdImport.update({
    id: '/schedules/data/forecasts/$forecastJobId',
    path: '/schedules/data/forecasts/$forecastJobId',
    getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseRoute =
  SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseImport.update({
    id: '/templates/$templateId/use',
    path: '/templates/$templateId/use',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdRoute =
  SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdImport.update({
    id: '/$checklistId/item/$itemId',
    path: '/$checklistId/item/$itemId',
    getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexImport.update({
    id: '/',
    path: '/',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexImport.update(
    {
      id: '/schedules/requests/availability/edit/',
      path: '/schedules/requests/availability/edit/',
      getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesImport.update(
    {
      id: '/wages',
      path: '/wages',
      getParentRoute: () =>
        SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsImport.update(
    {
      id: '/permissions',
      path: '/permissions',
      getParentRoute: () =>
        SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoImport.update({
    id: '/info',
    path: '/info',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalImport.update({
    id: '/personal',
    path: '/personal',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsImport.update(
    {
      id: '/contact-details',
      path: '/contact-details',
      getParentRoute: () =>
        SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditImport.update({
    id: '/job-titles/$jobId/edit',
    path: '/job-titles/$jobId/edit',
    getParentRoute: () => SignedinBusinessIdStoreIdNavStoreSettingsRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsImport.update({
    id: '/positions',
    path: '/positions',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditRoute =
  SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditImport.update({
    id: '/edit',
    path: '/edit',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRoute,
  } as any)

const SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdImport.update(
    {
      id: '/schedules/requests/timeOff/edit/$timeOffId',
      path: '/schedules/requests/timeOff/edit/$timeOffId',
      getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdRoute =
  SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdImport.update(
    {
      id: '/schedules/requests/availability/edit/$personAvailabilityId',
      path: '/schedules/requests/availability/edit/$personAvailabilityId',
      getParentRoute: () => SignedinBusinessIdStoreIdNavRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdRoute =
  SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdImport.update(
    {
      id: '/templates/$templateId/item/$itemId',
      path: '/templates/$templateId/item/$itemId',
      getParentRoute: () => SignedinBusinessIdStoreIdNavChecklistsRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditImport.update({
    id: '/note/edit',
    path: '/note/edit',
    getParentRoute: () =>
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute,
  } as any)

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdImport.update(
    {
      id: '/note/$noteId',
      path: '/note/$noteId',
      getParentRoute: () =>
        SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditImport.update(
    {
      id: '/correctiveAction/edit',
      path: '/correctiveAction/edit',
      getParentRoute: () =>
        SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdImport.update(
    {
      id: '/note/cmFromAi/$actionableItemId',
      path: '/note/cmFromAi/$actionableItemId',
      getParentRoute: () =>
        SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewImport.update(
    {
      id: '/correctiveAction/$correctiveActionId/view',
      path: '/correctiveAction/$correctiveActionId/view',
      getParentRoute: () =>
        SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute,
    } as any,
  )

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeRoute =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeImport.update(
    {
      id: '/correctiveAction/$correctiveActionId/formalize',
      path: '/correctiveAction/$correctiveActionId/formalize',
      getParentRoute: () =>
        SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute,
    } as any,
  )

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_signedin': {
      id: '/_signedin'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof SignedinImport
      parentRoute: typeof rootRoute
    }
    '/forgot-password': {
      id: '/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof ForgotPasswordImport
      parentRoute: typeof rootRoute
    }
    '/forgot-password-code': {
      id: '/forgot-password-code'
      path: '/forgot-password-code'
      fullPath: '/forgot-password-code'
      preLoaderRoute: typeof ForgotPasswordCodeImport
      parentRoute: typeof rootRoute
    }
    '/verify-email': {
      id: '/verify-email'
      path: '/verify-email'
      fullPath: '/verify-email'
      preLoaderRoute: typeof VerifyEmailImport
      parentRoute: typeof rootRoute
    }
    '/_signedin/admin': {
      id: '/_signedin/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof SignedinAdminImport
      parentRoute: typeof SignedinImport
    }
    '/import/': {
      id: '/import/'
      path: '/import'
      fullPath: '/import'
      preLoaderRoute: typeof ImportIndexImport
      parentRoute: typeof rootRoute
    }
    '/_signedin/admin/dashboard': {
      id: '/_signedin/admin/dashboard'
      path: '/dashboard'
      fullPath: '/admin/dashboard'
      preLoaderRoute: typeof SignedinAdminDashboardImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/$businessId/$storeId': {
      id: '/_signedin/$businessId/$storeId'
      path: '/$businessId/$storeId'
      fullPath: '/$businessId/$storeId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdImport
      parentRoute: typeof SignedinImport
    }
    '/_signedin/$businessId/$storeId/_nav': {
      id: '/_signedin/$businessId/$storeId/_nav'
      path: '/$businessId/$storeId'
      fullPath: '/$businessId/$storeId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavImport
      parentRoute: typeof SignedinBusinessIdStoreIdRoute
    }
    '/_signedin/admin/businesses/create': {
      id: '/_signedin/admin/businesses/create'
      path: '/businesses/create'
      fullPath: '/admin/businesses/create'
      preLoaderRoute: typeof SignedinAdminBusinessesCreateImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/education/$id': {
      id: '/_signedin/admin/education/$id'
      path: '/education/$id'
      fullPath: '/admin/education/$id'
      preLoaderRoute: typeof SignedinAdminEducationIdImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/education/create': {
      id: '/_signedin/admin/education/create'
      path: '/education/create'
      fullPath: '/admin/education/create'
      preLoaderRoute: typeof SignedinAdminEducationCreateImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/invitations/create': {
      id: '/_signedin/admin/invitations/create'
      path: '/invitations/create'
      fullPath: '/admin/invitations/create'
      preLoaderRoute: typeof SignedinAdminInvitationsCreateImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/newspaper/visibility': {
      id: '/_signedin/admin/newspaper/visibility'
      path: '/newspaper/visibility'
      fullPath: '/admin/newspaper/visibility'
      preLoaderRoute: typeof SignedinAdminNewspaperVisibilityImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/users/create': {
      id: '/_signedin/admin/users/create'
      path: '/users/create'
      fullPath: '/admin/users/create'
      preLoaderRoute: typeof SignedinAdminUsersCreateImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/$businessId/$storeId/': {
      id: '/_signedin/$businessId/$storeId/'
      path: '/'
      fullPath: '/$businessId/$storeId/'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/admin/businesses/': {
      id: '/_signedin/admin/businesses/'
      path: '/businesses'
      fullPath: '/admin/businesses'
      preLoaderRoute: typeof SignedinAdminBusinessesIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/education/': {
      id: '/_signedin/admin/education/'
      path: '/education'
      fullPath: '/admin/education'
      preLoaderRoute: typeof SignedinAdminEducationIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/features/': {
      id: '/_signedin/admin/features/'
      path: '/features'
      fullPath: '/admin/features'
      preLoaderRoute: typeof SignedinAdminFeaturesIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/invitations/': {
      id: '/_signedin/admin/invitations/'
      path: '/invitations'
      fullPath: '/admin/invitations'
      preLoaderRoute: typeof SignedinAdminInvitationsIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/newspaper/': {
      id: '/_signedin/admin/newspaper/'
      path: '/newspaper'
      fullPath: '/admin/newspaper'
      preLoaderRoute: typeof SignedinAdminNewspaperIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/onboarding/': {
      id: '/_signedin/admin/onboarding/'
      path: '/onboarding'
      fullPath: '/admin/onboarding'
      preLoaderRoute: typeof SignedinAdminOnboardingIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/people/': {
      id: '/_signedin/admin/people/'
      path: '/people'
      fullPath: '/admin/people'
      preLoaderRoute: typeof SignedinAdminPeopleIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/stores/': {
      id: '/_signedin/admin/stores/'
      path: '/stores'
      fullPath: '/admin/stores'
      preLoaderRoute: typeof SignedinAdminStoresIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/users/': {
      id: '/_signedin/admin/users/'
      path: '/users'
      fullPath: '/admin/users'
      preLoaderRoute: typeof SignedinAdminUsersIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/import/store/$id/': {
      id: '/import/store/$id/'
      path: '/import/store/$id'
      fullPath: '/import/store/$id'
      preLoaderRoute: typeof ImportStoreIdIndexImport
      parentRoute: typeof rootRoute
    }
    '/_signedin/$businessId/$storeId/_nav/education': {
      id: '/_signedin/$businessId/$storeId/_nav/education'
      path: '/education'
      fullPath: '/$businessId/$storeId/education'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavEducationImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/hr': {
      id: '/_signedin/$businessId/$storeId/_nav/hr'
      path: '/hr'
      fullPath: '/$businessId/$storeId/hr'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavHrImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/positions': {
      id: '/_signedin/$businessId/$storeId/_nav/positions'
      path: '/positions'
      fullPath: '/$businessId/$storeId/positions'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavPositionsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/settings': {
      id: '/_signedin/$businessId/$storeId/_nav/settings'
      path: '/settings'
      fullPath: '/$businessId/$storeId/settings'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSettingsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/store': {
      id: '/_signedin/$businessId/$storeId/_nav/store'
      path: '/store'
      fullPath: '/$businessId/$storeId/store'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/team': {
      id: '/_signedin/$businessId/$storeId/_nav/team'
      path: '/team'
      fullPath: '/$businessId/$storeId/team'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/timePunch': {
      id: '/_signedin/$businessId/$storeId/_nav/timePunch'
      path: '/timePunch'
      fullPath: '/$businessId/$storeId/timePunch'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTimePunchImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/training': {
      id: '/_signedin/$businessId/$storeId/_nav/training'
      path: '/training'
      fullPath: '/$businessId/$storeId/training'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTrainingImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/walkthrough': {
      id: '/_signedin/$businessId/$storeId/_nav/walkthrough'
      path: '/walkthrough'
      fullPath: '/$businessId/$storeId/walkthrough'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/onboarding/complete': {
      id: '/_signedin/$businessId/$storeId/onboarding/complete'
      path: '/onboarding/complete'
      fullPath: '/$businessId/$storeId/onboarding/complete'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdOnboardingCompleteImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/$businessId/$storeId/onboarding/laborLaws': {
      id: '/_signedin/$businessId/$storeId/onboarding/laborLaws'
      path: '/onboarding/laborLaws'
      fullPath: '/$businessId/$storeId/onboarding/laborLaws'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdOnboardingLaborLawsImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/$businessId/$storeId/onboarding/requiredBreaks': {
      id: '/_signedin/$businessId/$storeId/onboarding/requiredBreaks'
      path: '/onboarding/requiredBreaks'
      fullPath: '/$businessId/$storeId/onboarding/requiredBreaks'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdOnboardingRequiredBreaksImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/$businessId/$storeId/reports/availability': {
      id: '/_signedin/$businessId/$storeId/reports/availability'
      path: '/reports/availability'
      fullPath: '/$businessId/$storeId/reports/availability'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdReportsAvailabilityImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/$businessId/$storeId/reports/hourly-sales': {
      id: '/_signedin/$businessId/$storeId/reports/hourly-sales'
      path: '/reports/hourly-sales'
      fullPath: '/$businessId/$storeId/reports/hourly-sales'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdReportsHourlySalesImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/admin/businesses/$businessId/invite': {
      id: '/_signedin/admin/businesses/$businessId/invite'
      path: '/businesses/$businessId/invite'
      fullPath: '/admin/businesses/$businessId/invite'
      preLoaderRoute: typeof SignedinAdminBusinessesBusinessIdInviteImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/people/$personId/edit': {
      id: '/_signedin/admin/people/$personId/edit'
      path: '/people/$personId/edit'
      fullPath: '/admin/people/$personId/edit'
      preLoaderRoute: typeof SignedinAdminPeoplePersonIdEditImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/people/$personId/streamchat': {
      id: '/_signedin/admin/people/$personId/streamchat'
      path: '/people/$personId/streamchat'
      fullPath: '/admin/people/$personId/streamchat'
      preLoaderRoute: typeof SignedinAdminPeoplePersonIdStreamchatImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/users/$userId/edit': {
      id: '/_signedin/admin/users/$userId/edit'
      path: '/users/$userId/edit'
      fullPath: '/admin/users/$userId/edit'
      preLoaderRoute: typeof SignedinAdminUsersUserIdEditImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/$businessId/$storeId/onboarding/': {
      id: '/_signedin/$businessId/$storeId/onboarding/'
      path: '/onboarding'
      fullPath: '/$businessId/$storeId/onboarding'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdOnboardingIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/admin/businesses/$businessId/': {
      id: '/_signedin/admin/businesses/$businessId/'
      path: '/businesses/$businessId'
      fullPath: '/admin/businesses/$businessId'
      preLoaderRoute: typeof SignedinAdminBusinessesBusinessIdIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists'
      path: '/checklists'
      fullPath: '/$businessId/$storeId/checklists'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/_nav': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/_nav'
      path: '/checklists'
      fullPath: '/$businessId/$storeId/checklists'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsRoute
    }
    '/_signedin/$businessId/$storeId/_nav/devices/$clientId': {
      id: '/_signedin/$businessId/$storeId/_nav/devices/$clientId'
      path: '/devices/$clientId'
      fullPath: '/$businessId/$storeId/devices/$clientId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavDevicesClientIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/education/$id': {
      id: '/_signedin/$businessId/$storeId/_nav/education/$id'
      path: '/$id'
      fullPath: '/$businessId/$storeId/education/$id'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavEducationIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavEducationImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/builder': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/builder'
      path: '/schedules/builder'
      fullPath: '/$businessId/$storeId/schedules/builder'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesBuilderImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/insights': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/insights'
      path: '/schedules/insights'
      fullPath: '/$businessId/$storeId/schedules/insights'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesInsightsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/settings': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/settings'
      path: '/schedules/settings'
      fullPath: '/$businessId/$storeId/schedules/settings'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings'
      path: '/settings'
      fullPath: '/$businessId/$storeId/store/settings'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/walkthrough/$step': {
      id: '/_signedin/$businessId/$storeId/_nav/walkthrough/$step'
      path: '/$step'
      fullPath: '/$businessId/$storeId/walkthrough/$step'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughStepImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughImport
    }
    '/_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload': {
      id: '/_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload'
      path: '/team-member-upload'
      fullPath: '/$businessId/$storeId/walkthrough/team-member-upload'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughImport
    }
    '/_signedin/$businessId/$storeId/print/$scheduleId/daily': {
      id: '/_signedin/$businessId/$storeId/print/$scheduleId/daily'
      path: '/print/$scheduleId/daily'
      fullPath: '/$businessId/$storeId/print/$scheduleId/daily'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdPrintScheduleIdDailyImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/$businessId/$storeId/print/$scheduleId/team': {
      id: '/_signedin/$businessId/$storeId/print/$scheduleId/team'
      path: '/print/$scheduleId/team'
      fullPath: '/$businessId/$storeId/print/$scheduleId/team'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdPrintScheduleIdTeamImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/admin/businesses/$businessId/stores/create': {
      id: '/_signedin/admin/businesses/$businessId/stores/create'
      path: '/businesses/$businessId/stores/create'
      fullPath: '/admin/businesses/$businessId/stores/create'
      preLoaderRoute: typeof SignedinAdminBusinessesBusinessIdStoresCreateImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/newspaper/paper/$newspaperId/edit': {
      id: '/_signedin/admin/newspaper/paper/$newspaperId/edit'
      path: '/newspaper/paper/$newspaperId/edit'
      fullPath: '/admin/newspaper/paper/$newspaperId/edit'
      preLoaderRoute: typeof SignedinAdminNewspaperPaperNewspaperIdEditImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/newspaper/visibilityGroup/$visibilityGroupId/edit': {
      id: '/_signedin/admin/newspaper/visibilityGroup/$visibilityGroupId/edit'
      path: '/newspaper/visibilityGroup/$visibilityGroupId/edit'
      fullPath: '/admin/newspaper/visibilityGroup/$visibilityGroupId/edit'
      preLoaderRoute: typeof SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/$businessId/$storeId/_nav/devices/': {
      id: '/_signedin/$businessId/$storeId/_nav/devices/'
      path: '/devices'
      fullPath: '/$businessId/$storeId/devices'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavDevicesIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/education/': {
      id: '/_signedin/$businessId/$storeId/_nav/education/'
      path: '/'
      fullPath: '/$businessId/$storeId/education/'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavEducationIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavEducationImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/'
      path: '/schedules'
      fullPath: '/$businessId/$storeId/schedules'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/walkthrough/': {
      id: '/_signedin/$businessId/$storeId/_nav/walkthrough/'
      path: '/'
      fullPath: '/$businessId/$storeId/walkthrough/'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughImport
    }
    '/_signedin/$businessId/$storeId/schedules/$scheduleId/': {
      id: '/_signedin/$businessId/$storeId/schedules/$scheduleId/'
      path: '/schedules/$scheduleId'
      fullPath: '/$businessId/$storeId/schedules/$scheduleId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdSchedulesScheduleIdIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view'
      path: '/$checklistId/view'
      fullPath: '/$businessId/$storeId/checklists/$checklistId/view'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/_nav/active': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/_nav/active'
      path: '/active'
      fullPath: '/$businessId/$storeId/checklists/active'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavActiveImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded'
      path: '/recorded'
      fullPath: '/$businessId/$storeId/checklists/recorded'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavRecordedImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/_nav/templates': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/_nav/templates'
      path: '/templates'
      fullPath: '/$businessId/$storeId/checklists/templates'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavTemplatesImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/_nav/upcoming': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/_nav/upcoming'
      path: '/upcoming'
      fullPath: '/$businessId/$storeId/checklists/upcoming'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavUpcomingImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/hr/documents/edit': {
      id: '/_signedin/$businessId/$storeId/_nav/hr/documents/edit'
      path: '/documents/edit'
      fullPath: '/$businessId/$storeId/hr/documents/edit'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavHrDocumentsEditImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavHrImport
    }
    '/_signedin/$businessId/$storeId/_nav/hr/documents/view': {
      id: '/_signedin/$businessId/$storeId/_nav/hr/documents/view'
      path: '/documents/view'
      fullPath: '/$businessId/$storeId/hr/documents/view'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavHrDocumentsViewImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavHrImport
    }
    '/_signedin/$businessId/$storeId/_nav/hr/reminders/edit': {
      id: '/_signedin/$businessId/$storeId/_nav/hr/reminders/edit'
      path: '/reminders/edit'
      fullPath: '/$businessId/$storeId/hr/reminders/edit'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavHrRemindersEditImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavHrImport
    }
    '/_signedin/$businessId/$storeId/_nav/hr/reminders/view': {
      id: '/_signedin/$businessId/$storeId/_nav/hr/reminders/view'
      path: '/reminders/view'
      fullPath: '/$businessId/$storeId/hr/reminders/view'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavHrRemindersViewImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavHrImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/$scheduleId/metrics': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/$scheduleId/metrics'
      path: '/schedules/$scheduleId/metrics'
      fullPath: '/$businessId/$storeId/schedules/$scheduleId/metrics'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/data/$dataFileId': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/data/$dataFileId'
      path: '/schedules/data/$dataFileId'
      fullPath: '/$businessId/$storeId/schedules/data/$dataFileId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/data/upload': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/data/upload'
      path: '/schedules/data/upload'
      fullPath: '/$businessId/$storeId/schedules/data/upload'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataUploadImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/settings/availability': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/settings/availability'
      path: '/availability'
      fullPath: '/$businessId/$storeId/schedules/settings/availability'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/settings/break-rules': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/settings/break-rules'
      path: '/break-rules'
      fullPath: '/$businessId/$storeId/schedules/settings/break-rules'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/settings/labor-laws': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/settings/labor-laws'
      path: '/labor-laws'
      fullPath: '/$businessId/$storeId/schedules/settings/labor-laws'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/settings/time-off': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/settings/time-off'
      path: '/time-off'
      fullPath: '/$businessId/$storeId/schedules/settings/time-off'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/settings/validations': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/settings/validations'
      path: '/validations'
      fullPath: '/$businessId/$storeId/schedules/settings/validations'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/announcements/edit': {
      id: '/_signedin/$businessId/$storeId/_nav/store/announcements/edit'
      path: '/announcements/edit'
      fullPath: '/$businessId/$storeId/store/announcements/edit'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsEditImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/announcements/view': {
      id: '/_signedin/$businessId/$storeId/_nav/store/announcements/view'
      path: '/announcements/view'
      fullPath: '/$businessId/$storeId/store/announcements/view'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsViewImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/links/$link': {
      id: '/_signedin/$businessId/$storeId/_nav/store/links/$link'
      path: '/links/$link'
      fullPath: '/$businessId/$storeId/store/links/$link'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreLinksLinkImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/core-values': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/core-values'
      path: '/core-values'
      fullPath: '/$businessId/$storeId/store/settings/core-values'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/store-info': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/store-info'
      path: '/store-info'
      fullPath: '/$businessId/$storeId/store/settings/store-info'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/work-mode': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/work-mode'
      path: '/work-mode'
      fullPath: '/$businessId/$storeId/store/settings/work-mode'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsWorkModeImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId'
      path: '/directory/$personId'
      fullPath: '/$businessId/$storeId/team/directory/$personId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamImport
    }
    '/_signedin/$businessId/$storeId/print/insights/scheduling/shifts': {
      id: '/_signedin/$businessId/$storeId/print/insights/scheduling/shifts'
      path: '/print/insights/scheduling/shifts'
      fullPath: '/$businessId/$storeId/print/insights/scheduling/shifts'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/$businessId/$storeId/print/insights/scheduling/table': {
      id: '/_signedin/$businessId/$storeId/print/insights/scheduling/table'
      path: '/print/insights/scheduling/table'
      fullPath: '/$businessId/$storeId/print/insights/scheduling/table'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingTableImport
      parentRoute: typeof SignedinBusinessIdStoreIdImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/'
      path: '/$checklistId'
      fullPath: '/$businessId/$storeId/checklists/$checklistId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/_nav/': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/_nav/'
      path: '/'
      fullPath: '/$businessId/$storeId/checklists/'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/hr/documents/': {
      id: '/_signedin/$businessId/$storeId/_nav/hr/documents/'
      path: '/documents'
      fullPath: '/$businessId/$storeId/hr/documents'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavHrDocumentsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavHrImport
    }
    '/_signedin/$businessId/$storeId/_nav/hr/insights/': {
      id: '/_signedin/$businessId/$storeId/_nav/hr/insights/'
      path: '/insights'
      fullPath: '/$businessId/$storeId/hr/insights'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavHrInsightsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavHrImport
    }
    '/_signedin/$businessId/$storeId/_nav/hr/reminders/': {
      id: '/_signedin/$businessId/$storeId/_nav/hr/reminders/'
      path: '/reminders'
      fullPath: '/$businessId/$storeId/hr/reminders'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavHrRemindersIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavHrImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/data/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/data/'
      path: '/schedules/data'
      fullPath: '/$businessId/$storeId/schedules/data'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/reports/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/reports/'
      path: '/schedules/reports'
      fullPath: '/$businessId/$storeId/schedules/reports'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesReportsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/'
      path: '/schedules/requests'
      fullPath: '/$businessId/$storeId/schedules/requests'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/settings/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/settings/'
      path: '/'
      fullPath: '/$businessId/$storeId/schedules/settings/'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/announcements/': {
      id: '/_signedin/$businessId/$storeId/_nav/store/announcements/'
      path: '/announcements'
      fullPath: '/$businessId/$storeId/store/announcements'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/events/': {
      id: '/_signedin/$businessId/$storeId/_nav/store/events/'
      path: '/events'
      fullPath: '/$businessId/$storeId/store/events'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreEventsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/files/': {
      id: '/_signedin/$businessId/$storeId/_nav/store/files/'
      path: '/files'
      fullPath: '/$businessId/$storeId/store/files'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreFilesIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/links/': {
      id: '/_signedin/$businessId/$storeId/_nav/store/links/'
      path: '/links'
      fullPath: '/$businessId/$storeId/store/links'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreLinksIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/'
      path: '/directory'
      fullPath: '/$businessId/$storeId/team/directory'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/reports/': {
      id: '/_signedin/$businessId/$storeId/_nav/team/reports/'
      path: '/reports'
      fullPath: '/$businessId/$storeId/team/reports'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamReportsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamImport
    }
    '/_signedin/$businessId/$storeId/_nav/timePunch/punches/': {
      id: '/_signedin/$businessId/$storeId/_nav/timePunch/punches/'
      path: '/punches'
      fullPath: '/$businessId/$storeId/timePunch/punches'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTimePunchPunchesIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTimePunchImport
    }
    '/_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/': {
      id: '/_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/'
      path: '/teamMemberVariance'
      fullPath: '/$businessId/$storeId/timePunch/teamMemberVariance'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTimePunchImport
    }
    '/_signedin/$businessId/$storeId/_nav/timePunch/teamTotals/': {
      id: '/_signedin/$businessId/$storeId/_nav/timePunch/teamTotals/'
      path: '/teamTotals'
      fullPath: '/$businessId/$storeId/timePunch/teamTotals'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTimePunchImport
    }
    '/_signedin/$businessId/$storeId/_nav/timePunch/variance/': {
      id: '/_signedin/$businessId/$storeId/_nav/timePunch/variance/'
      path: '/variance'
      fullPath: '/$businessId/$storeId/timePunch/variance'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTimePunchVarianceIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTimePunchImport
    }
    '/_signedin/$businessId/$storeId/_nav/training/insights/': {
      id: '/_signedin/$businessId/$storeId/_nav/training/insights/'
      path: '/insights'
      fullPath: '/$businessId/$storeId/training/insights'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTrainingInsightsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTrainingImport
    }
    '/_signedin/admin/businesses/$businessId/stores/$storeId/': {
      id: '/_signedin/admin/businesses/$businessId/stores/$storeId/'
      path: '/businesses/$businessId/stores/$storeId'
      fullPath: '/admin/businesses/$businessId/stores/$storeId'
      preLoaderRoute: typeof SignedinAdminBusinessesBusinessIdStoresStoreIdIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId'
      path: '/$checklistId/item/$itemId'
      fullPath: '/$businessId/$storeId/checklists/$checklistId/item/$itemId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use'
      path: '/templates/$templateId/use'
      fullPath: '/$businessId/$storeId/checklists/templates/$templateId/use'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/$forecastJobId': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/$forecastJobId'
      path: '/schedules/data/forecasts/$forecastJobId'
      fullPath: '/$businessId/$storeId/schedules/data/forecasts/$forecastJobId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/$personAvailabilityId': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/$personAvailabilityId'
      path: '/schedules/requests/availability/$personAvailabilityId'
      fullPath: '/$businessId/$storeId/schedules/requests/availability/$personAvailabilityId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/$shiftOfferId': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/$shiftOfferId'
      path: '/schedules/requests/shiftOffers/$shiftOfferId'
      fullPath: '/$businessId/$storeId/schedules/requests/shiftOffers/$shiftOfferId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/$timeOffId': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/$timeOffId'
      path: '/schedules/requests/timeOff/$timeOffId'
      fullPath: '/$businessId/$storeId/schedules/requests/timeOff/$timeOffId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create'
      path: '/schedules/requests/timeOff/create'
      fullPath: '/$businessId/$storeId/schedules/requests/timeOff/create'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/files/$fileId/view': {
      id: '/_signedin/$businessId/$storeId/_nav/store/files/$fileId/view'
      path: '/files/$fileId/view'
      fullPath: '/$businessId/$storeId/store/files/$fileId/view'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreFilesFileIdViewImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId'
      path: '/area/$areaId'
      fullPath: '/$businessId/$storeId/store/settings/area/$areaId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/area/work-areas': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/area/work-areas'
      path: '/area/work-areas'
      fullPath: '/$businessId/$storeId/store/settings/area/work-areas'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about'
      path: '/about'
      fullPath: '/$businessId/$storeId/team/directory/$personId/about'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/documents': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/documents'
      path: '/documents'
      fullPath: '/$businessId/$storeId/team/directory/$personId/documents'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment'
      path: '/employment'
      fullPath: '/$businessId/$storeId/team/directory/$personId/employment'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes'
      path: '/notes'
      fullPath: '/$businessId/$storeId/team/directory/$personId/notes'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/positions': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/positions'
      path: '/positions'
      fullPath: '/$businessId/$storeId/team/directory/$personId/positions'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdImport
    }
    '/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/import': {
      id: '/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/import'
      path: '/businesses/$businessId/stores/$storeId/checklists/import'
      fullPath: '/admin/businesses/$businessId/stores/$storeId/checklists/import'
      preLoaderRoute: typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/admin/businesses/$businessId/stores/$storeId/person/$personId': {
      id: '/_signedin/admin/businesses/$businessId/stores/$storeId/person/$personId'
      path: '/businesses/$businessId/stores/$storeId/person/$personId'
      fullPath: '/admin/businesses/$businessId/stores/$storeId/person/$personId'
      preLoaderRoute: typeof SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/'
      path: '/templates/$templateId'
      fullPath: '/$businessId/$storeId/checklists/templates/$templateId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/data/files/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/data/files/'
      path: '/schedules/data/files'
      fullPath: '/$businessId/$storeId/schedules/data/files'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/'
      path: '/schedules/data/forecasts'
      fullPath: '/$businessId/$storeId/schedules/data/forecasts'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/'
      path: '/schedules/requests/availability'
      fullPath: '/$businessId/$storeId/schedules/requests/availability'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/'
      path: '/schedules/requests/shiftOffers'
      fullPath: '/$businessId/$storeId/schedules/requests/shiftOffers'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/'
      path: '/schedules/requests/timeOff'
      fullPath: '/$businessId/$storeId/schedules/requests/timeOff'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/'
      path: '/job-titles'
      fullPath: '/$businessId/$storeId/store/settings/job-titles'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsImport
    }
    '/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/': {
      id: '/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/'
      path: '/businesses/$businessId/stores/$storeId/checklists'
      fullPath: '/admin/businesses/$businessId/stores/$storeId/checklists'
      preLoaderRoute: typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexImport
      parentRoute: typeof SignedinAdminImport
    }
    '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId': {
      id: '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId'
      path: '/templates/$templateId/item/$itemId'
      fullPath: '/$businessId/$storeId/checklists/templates/$templateId/item/$itemId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavChecklistsImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/$personAvailabilityId': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/$personAvailabilityId'
      path: '/schedules/requests/availability/edit/$personAvailabilityId'
      fullPath: '/$businessId/$storeId/schedules/requests/availability/edit/$personAvailabilityId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/edit/$timeOffId': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/edit/$timeOffId'
      path: '/schedules/requests/timeOff/edit/$timeOffId'
      fullPath: '/$businessId/$storeId/schedules/requests/timeOff/edit/$timeOffId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/edit': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/edit'
      path: '/edit'
      fullPath: '/$businessId/$storeId/store/settings/area/$areaId/edit'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/positions': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/positions'
      path: '/positions'
      fullPath: '/$businessId/$storeId/store/settings/area/$areaId/positions'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdImport
    }
    '/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/$jobId/edit': {
      id: '/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/$jobId/edit'
      path: '/job-titles/$jobId/edit'
      fullPath: '/$businessId/$storeId/store/settings/job-titles/$jobId/edit'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/contact-details': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/contact-details'
      path: '/contact-details'
      fullPath: '/$businessId/$storeId/team/directory/$personId/about/contact-details'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/personal': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/personal'
      path: '/personal'
      fullPath: '/$businessId/$storeId/team/directory/$personId/about/personal'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/info': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/info'
      path: '/info'
      fullPath: '/$businessId/$storeId/team/directory/$personId/employment/info'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/permissions': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/permissions'
      path: '/permissions'
      fullPath: '/$businessId/$storeId/team/directory/$personId/employment/permissions'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/wages': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/wages'
      path: '/wages'
      fullPath: '/$businessId/$storeId/team/directory/$personId/employment/wages'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentImport
    }
    '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/': {
      id: '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/'
      path: '/schedules/requests/availability/edit'
      fullPath: '/$businessId/$storeId/schedules/requests/availability/edit'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/'
      path: '/'
      fullPath: '/$businessId/$storeId/team/directory/$personId/notes/'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit'
      path: '/correctiveAction/edit'
      fullPath: '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/edit'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/$noteId': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/$noteId'
      path: '/note/$noteId'
      fullPath: '/$businessId/$storeId/team/directory/$personId/notes/note/$noteId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/edit': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/edit'
      path: '/note/edit'
      fullPath: '/$businessId/$storeId/team/directory/$personId/notes/note/edit'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize'
      path: '/correctiveAction/$correctiveActionId/formalize'
      fullPath: '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view'
      path: '/correctiveAction/$correctiveActionId/view'
      fullPath: '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport
    }
    '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/cmFromAi/$actionableItemId': {
      id: '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/cmFromAi/$actionableItemId'
      path: '/note/cmFromAi/$actionableItemId'
      fullPath: '/$businessId/$storeId/team/directory/$personId/notes/note/cmFromAi/$actionableItemId'
      preLoaderRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdImport
      parentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesImport
    }
  }
}

// Create and export the route tree

interface SignedinAdminRouteChildren {
  SignedinAdminDashboardRoute: typeof SignedinAdminDashboardRoute
  SignedinAdminBusinessesCreateRoute: typeof SignedinAdminBusinessesCreateRoute
  SignedinAdminEducationIdRoute: typeof SignedinAdminEducationIdRoute
  SignedinAdminEducationCreateRoute: typeof SignedinAdminEducationCreateRoute
  SignedinAdminInvitationsCreateRoute: typeof SignedinAdminInvitationsCreateRoute
  SignedinAdminNewspaperVisibilityRoute: typeof SignedinAdminNewspaperVisibilityRoute
  SignedinAdminUsersCreateRoute: typeof SignedinAdminUsersCreateRoute
  SignedinAdminBusinessesIndexRoute: typeof SignedinAdminBusinessesIndexRoute
  SignedinAdminEducationIndexRoute: typeof SignedinAdminEducationIndexRoute
  SignedinAdminFeaturesIndexRoute: typeof SignedinAdminFeaturesIndexRoute
  SignedinAdminInvitationsIndexRoute: typeof SignedinAdminInvitationsIndexRoute
  SignedinAdminNewspaperIndexRoute: typeof SignedinAdminNewspaperIndexRoute
  SignedinAdminOnboardingIndexRoute: typeof SignedinAdminOnboardingIndexRoute
  SignedinAdminPeopleIndexRoute: typeof SignedinAdminPeopleIndexRoute
  SignedinAdminStoresIndexRoute: typeof SignedinAdminStoresIndexRoute
  SignedinAdminUsersIndexRoute: typeof SignedinAdminUsersIndexRoute
  SignedinAdminBusinessesBusinessIdInviteRoute: typeof SignedinAdminBusinessesBusinessIdInviteRoute
  SignedinAdminPeoplePersonIdEditRoute: typeof SignedinAdminPeoplePersonIdEditRoute
  SignedinAdminPeoplePersonIdStreamchatRoute: typeof SignedinAdminPeoplePersonIdStreamchatRoute
  SignedinAdminUsersUserIdEditRoute: typeof SignedinAdminUsersUserIdEditRoute
  SignedinAdminBusinessesBusinessIdIndexRoute: typeof SignedinAdminBusinessesBusinessIdIndexRoute
  SignedinAdminBusinessesBusinessIdStoresCreateRoute: typeof SignedinAdminBusinessesBusinessIdStoresCreateRoute
  SignedinAdminNewspaperPaperNewspaperIdEditRoute: typeof SignedinAdminNewspaperPaperNewspaperIdEditRoute
  SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditRoute: typeof SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditRoute
  SignedinAdminBusinessesBusinessIdStoresStoreIdIndexRoute: typeof SignedinAdminBusinessesBusinessIdStoresStoreIdIndexRoute
  SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportRoute: typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportRoute
  SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdRoute: typeof SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdRoute
  SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexRoute: typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexRoute
}

const SignedinAdminRouteChildren: SignedinAdminRouteChildren = {
  SignedinAdminDashboardRoute: SignedinAdminDashboardRoute,
  SignedinAdminBusinessesCreateRoute: SignedinAdminBusinessesCreateRoute,
  SignedinAdminEducationIdRoute: SignedinAdminEducationIdRoute,
  SignedinAdminEducationCreateRoute: SignedinAdminEducationCreateRoute,
  SignedinAdminInvitationsCreateRoute: SignedinAdminInvitationsCreateRoute,
  SignedinAdminNewspaperVisibilityRoute: SignedinAdminNewspaperVisibilityRoute,
  SignedinAdminUsersCreateRoute: SignedinAdminUsersCreateRoute,
  SignedinAdminBusinessesIndexRoute: SignedinAdminBusinessesIndexRoute,
  SignedinAdminEducationIndexRoute: SignedinAdminEducationIndexRoute,
  SignedinAdminFeaturesIndexRoute: SignedinAdminFeaturesIndexRoute,
  SignedinAdminInvitationsIndexRoute: SignedinAdminInvitationsIndexRoute,
  SignedinAdminNewspaperIndexRoute: SignedinAdminNewspaperIndexRoute,
  SignedinAdminOnboardingIndexRoute: SignedinAdminOnboardingIndexRoute,
  SignedinAdminPeopleIndexRoute: SignedinAdminPeopleIndexRoute,
  SignedinAdminStoresIndexRoute: SignedinAdminStoresIndexRoute,
  SignedinAdminUsersIndexRoute: SignedinAdminUsersIndexRoute,
  SignedinAdminBusinessesBusinessIdInviteRoute:
    SignedinAdminBusinessesBusinessIdInviteRoute,
  SignedinAdminPeoplePersonIdEditRoute: SignedinAdminPeoplePersonIdEditRoute,
  SignedinAdminPeoplePersonIdStreamchatRoute:
    SignedinAdminPeoplePersonIdStreamchatRoute,
  SignedinAdminUsersUserIdEditRoute: SignedinAdminUsersUserIdEditRoute,
  SignedinAdminBusinessesBusinessIdIndexRoute:
    SignedinAdminBusinessesBusinessIdIndexRoute,
  SignedinAdminBusinessesBusinessIdStoresCreateRoute:
    SignedinAdminBusinessesBusinessIdStoresCreateRoute,
  SignedinAdminNewspaperPaperNewspaperIdEditRoute:
    SignedinAdminNewspaperPaperNewspaperIdEditRoute,
  SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditRoute:
    SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditRoute,
  SignedinAdminBusinessesBusinessIdStoresStoreIdIndexRoute:
    SignedinAdminBusinessesBusinessIdStoresStoreIdIndexRoute,
  SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportRoute:
    SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportRoute,
  SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdRoute:
    SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdRoute,
  SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexRoute:
    SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexRoute,
}

const SignedinAdminRouteWithChildren = SignedinAdminRoute._addFileChildren(
  SignedinAdminRouteChildren,
)

interface SignedinBusinessIdStoreIdNavEducationRouteChildren {
  SignedinBusinessIdStoreIdNavEducationIdRoute: typeof SignedinBusinessIdStoreIdNavEducationIdRoute
  SignedinBusinessIdStoreIdNavEducationIndexRoute: typeof SignedinBusinessIdStoreIdNavEducationIndexRoute
}

const SignedinBusinessIdStoreIdNavEducationRouteChildren: SignedinBusinessIdStoreIdNavEducationRouteChildren =
  {
    SignedinBusinessIdStoreIdNavEducationIdRoute:
      SignedinBusinessIdStoreIdNavEducationIdRoute,
    SignedinBusinessIdStoreIdNavEducationIndexRoute:
      SignedinBusinessIdStoreIdNavEducationIndexRoute,
  }

const SignedinBusinessIdStoreIdNavEducationRouteWithChildren =
  SignedinBusinessIdStoreIdNavEducationRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavEducationRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavHrRouteChildren {
  SignedinBusinessIdStoreIdNavHrDocumentsEditRoute: typeof SignedinBusinessIdStoreIdNavHrDocumentsEditRoute
  SignedinBusinessIdStoreIdNavHrDocumentsViewRoute: typeof SignedinBusinessIdStoreIdNavHrDocumentsViewRoute
  SignedinBusinessIdStoreIdNavHrRemindersEditRoute: typeof SignedinBusinessIdStoreIdNavHrRemindersEditRoute
  SignedinBusinessIdStoreIdNavHrRemindersViewRoute: typeof SignedinBusinessIdStoreIdNavHrRemindersViewRoute
  SignedinBusinessIdStoreIdNavHrDocumentsIndexRoute: typeof SignedinBusinessIdStoreIdNavHrDocumentsIndexRoute
  SignedinBusinessIdStoreIdNavHrInsightsIndexRoute: typeof SignedinBusinessIdStoreIdNavHrInsightsIndexRoute
  SignedinBusinessIdStoreIdNavHrRemindersIndexRoute: typeof SignedinBusinessIdStoreIdNavHrRemindersIndexRoute
}

const SignedinBusinessIdStoreIdNavHrRouteChildren: SignedinBusinessIdStoreIdNavHrRouteChildren =
  {
    SignedinBusinessIdStoreIdNavHrDocumentsEditRoute:
      SignedinBusinessIdStoreIdNavHrDocumentsEditRoute,
    SignedinBusinessIdStoreIdNavHrDocumentsViewRoute:
      SignedinBusinessIdStoreIdNavHrDocumentsViewRoute,
    SignedinBusinessIdStoreIdNavHrRemindersEditRoute:
      SignedinBusinessIdStoreIdNavHrRemindersEditRoute,
    SignedinBusinessIdStoreIdNavHrRemindersViewRoute:
      SignedinBusinessIdStoreIdNavHrRemindersViewRoute,
    SignedinBusinessIdStoreIdNavHrDocumentsIndexRoute:
      SignedinBusinessIdStoreIdNavHrDocumentsIndexRoute,
    SignedinBusinessIdStoreIdNavHrInsightsIndexRoute:
      SignedinBusinessIdStoreIdNavHrInsightsIndexRoute,
    SignedinBusinessIdStoreIdNavHrRemindersIndexRoute:
      SignedinBusinessIdStoreIdNavHrRemindersIndexRoute,
  }

const SignedinBusinessIdStoreIdNavHrRouteWithChildren =
  SignedinBusinessIdStoreIdNavHrRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavHrRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteChildren {
  SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditRoute
  SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsRoute
}

const SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteChildren: SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteChildren =
  {
    SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditRoute,
    SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsRoute,
  }

const SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteWithChildren =
  SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavStoreSettingsRouteChildren {
  SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesRoute
  SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoRoute
  SignedinBusinessIdStoreIdNavStoreSettingsWorkModeRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsWorkModeRoute
  SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteWithChildren
  SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasRoute
  SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexRoute
  SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditRoute
}

const SignedinBusinessIdStoreIdNavStoreSettingsRouteChildren: SignedinBusinessIdStoreIdNavStoreSettingsRouteChildren =
  {
    SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesRoute,
    SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoRoute,
    SignedinBusinessIdStoreIdNavStoreSettingsWorkModeRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsWorkModeRoute,
    SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteWithChildren,
    SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasRoute,
    SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexRoute,
    SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditRoute,
  }

const SignedinBusinessIdStoreIdNavStoreSettingsRouteWithChildren =
  SignedinBusinessIdStoreIdNavStoreSettingsRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavStoreSettingsRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavStoreRouteChildren {
  SignedinBusinessIdStoreIdNavStoreSettingsRoute: typeof SignedinBusinessIdStoreIdNavStoreSettingsRouteWithChildren
  SignedinBusinessIdStoreIdNavStoreAnnouncementsEditRoute: typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsEditRoute
  SignedinBusinessIdStoreIdNavStoreAnnouncementsViewRoute: typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsViewRoute
  SignedinBusinessIdStoreIdNavStoreLinksLinkRoute: typeof SignedinBusinessIdStoreIdNavStoreLinksLinkRoute
  SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexRoute: typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexRoute
  SignedinBusinessIdStoreIdNavStoreEventsIndexRoute: typeof SignedinBusinessIdStoreIdNavStoreEventsIndexRoute
  SignedinBusinessIdStoreIdNavStoreFilesIndexRoute: typeof SignedinBusinessIdStoreIdNavStoreFilesIndexRoute
  SignedinBusinessIdStoreIdNavStoreLinksIndexRoute: typeof SignedinBusinessIdStoreIdNavStoreLinksIndexRoute
  SignedinBusinessIdStoreIdNavStoreFilesFileIdViewRoute: typeof SignedinBusinessIdStoreIdNavStoreFilesFileIdViewRoute
}

const SignedinBusinessIdStoreIdNavStoreRouteChildren: SignedinBusinessIdStoreIdNavStoreRouteChildren =
  {
    SignedinBusinessIdStoreIdNavStoreSettingsRoute:
      SignedinBusinessIdStoreIdNavStoreSettingsRouteWithChildren,
    SignedinBusinessIdStoreIdNavStoreAnnouncementsEditRoute:
      SignedinBusinessIdStoreIdNavStoreAnnouncementsEditRoute,
    SignedinBusinessIdStoreIdNavStoreAnnouncementsViewRoute:
      SignedinBusinessIdStoreIdNavStoreAnnouncementsViewRoute,
    SignedinBusinessIdStoreIdNavStoreLinksLinkRoute:
      SignedinBusinessIdStoreIdNavStoreLinksLinkRoute,
    SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexRoute:
      SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexRoute,
    SignedinBusinessIdStoreIdNavStoreEventsIndexRoute:
      SignedinBusinessIdStoreIdNavStoreEventsIndexRoute,
    SignedinBusinessIdStoreIdNavStoreFilesIndexRoute:
      SignedinBusinessIdStoreIdNavStoreFilesIndexRoute,
    SignedinBusinessIdStoreIdNavStoreLinksIndexRoute:
      SignedinBusinessIdStoreIdNavStoreLinksIndexRoute,
    SignedinBusinessIdStoreIdNavStoreFilesFileIdViewRoute:
      SignedinBusinessIdStoreIdNavStoreFilesFileIdViewRoute,
  }

const SignedinBusinessIdStoreIdNavStoreRouteWithChildren =
  SignedinBusinessIdStoreIdNavStoreRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavStoreRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteChildren {
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalRoute
}

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteChildren: SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteChildren =
  {
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalRoute,
  }

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteWithChildren =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteChildren {
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesRoute
}

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteChildren: SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteChildren =
  {
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesRoute,
  }

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteWithChildren =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteChildren {
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdRoute
}

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteChildren: SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteChildren =
  {
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdRoute,
  }

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteWithChildren =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteChildren {
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteWithChildren
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsRoute
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteWithChildren
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteWithChildren
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsRoute
}

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteChildren: SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteChildren =
  {
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteWithChildren,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsRoute,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteWithChildren,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteWithChildren,
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsRoute,
  }

const SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteWithChildren =
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavTeamRouteChildren {
  SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteWithChildren
  SignedinBusinessIdStoreIdNavTeamDirectoryIndexRoute: typeof SignedinBusinessIdStoreIdNavTeamDirectoryIndexRoute
  SignedinBusinessIdStoreIdNavTeamReportsIndexRoute: typeof SignedinBusinessIdStoreIdNavTeamReportsIndexRoute
}

const SignedinBusinessIdStoreIdNavTeamRouteChildren: SignedinBusinessIdStoreIdNavTeamRouteChildren =
  {
    SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteWithChildren,
    SignedinBusinessIdStoreIdNavTeamDirectoryIndexRoute:
      SignedinBusinessIdStoreIdNavTeamDirectoryIndexRoute,
    SignedinBusinessIdStoreIdNavTeamReportsIndexRoute:
      SignedinBusinessIdStoreIdNavTeamReportsIndexRoute,
  }

const SignedinBusinessIdStoreIdNavTeamRouteWithChildren =
  SignedinBusinessIdStoreIdNavTeamRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavTeamRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavTimePunchRouteChildren {
  SignedinBusinessIdStoreIdNavTimePunchPunchesIndexRoute: typeof SignedinBusinessIdStoreIdNavTimePunchPunchesIndexRoute
  SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexRoute: typeof SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexRoute
  SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexRoute: typeof SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexRoute
  SignedinBusinessIdStoreIdNavTimePunchVarianceIndexRoute: typeof SignedinBusinessIdStoreIdNavTimePunchVarianceIndexRoute
}

const SignedinBusinessIdStoreIdNavTimePunchRouteChildren: SignedinBusinessIdStoreIdNavTimePunchRouteChildren =
  {
    SignedinBusinessIdStoreIdNavTimePunchPunchesIndexRoute:
      SignedinBusinessIdStoreIdNavTimePunchPunchesIndexRoute,
    SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexRoute:
      SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexRoute,
    SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexRoute:
      SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexRoute,
    SignedinBusinessIdStoreIdNavTimePunchVarianceIndexRoute:
      SignedinBusinessIdStoreIdNavTimePunchVarianceIndexRoute,
  }

const SignedinBusinessIdStoreIdNavTimePunchRouteWithChildren =
  SignedinBusinessIdStoreIdNavTimePunchRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavTimePunchRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavTrainingRouteChildren {
  SignedinBusinessIdStoreIdNavTrainingInsightsIndexRoute: typeof SignedinBusinessIdStoreIdNavTrainingInsightsIndexRoute
}

const SignedinBusinessIdStoreIdNavTrainingRouteChildren: SignedinBusinessIdStoreIdNavTrainingRouteChildren =
  {
    SignedinBusinessIdStoreIdNavTrainingInsightsIndexRoute:
      SignedinBusinessIdStoreIdNavTrainingInsightsIndexRoute,
  }

const SignedinBusinessIdStoreIdNavTrainingRouteWithChildren =
  SignedinBusinessIdStoreIdNavTrainingRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavTrainingRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavWalkthroughRouteChildren {
  SignedinBusinessIdStoreIdNavWalkthroughStepRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughStepRoute
  SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadRoute
  SignedinBusinessIdStoreIdNavWalkthroughIndexRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughIndexRoute
}

const SignedinBusinessIdStoreIdNavWalkthroughRouteChildren: SignedinBusinessIdStoreIdNavWalkthroughRouteChildren =
  {
    SignedinBusinessIdStoreIdNavWalkthroughStepRoute:
      SignedinBusinessIdStoreIdNavWalkthroughStepRoute,
    SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadRoute:
      SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadRoute,
    SignedinBusinessIdStoreIdNavWalkthroughIndexRoute:
      SignedinBusinessIdStoreIdNavWalkthroughIndexRoute,
  }

const SignedinBusinessIdStoreIdNavWalkthroughRouteWithChildren =
  SignedinBusinessIdStoreIdNavWalkthroughRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavWalkthroughRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavChecklistsNavRouteChildren {
  SignedinBusinessIdStoreIdNavChecklistsNavActiveRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavActiveRoute
  SignedinBusinessIdStoreIdNavChecklistsNavRecordedRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavRecordedRoute
  SignedinBusinessIdStoreIdNavChecklistsNavTemplatesRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavTemplatesRoute
  SignedinBusinessIdStoreIdNavChecklistsNavUpcomingRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavUpcomingRoute
  SignedinBusinessIdStoreIdNavChecklistsNavIndexRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavIndexRoute
}

const SignedinBusinessIdStoreIdNavChecklistsNavRouteChildren: SignedinBusinessIdStoreIdNavChecklistsNavRouteChildren =
  {
    SignedinBusinessIdStoreIdNavChecklistsNavActiveRoute:
      SignedinBusinessIdStoreIdNavChecklistsNavActiveRoute,
    SignedinBusinessIdStoreIdNavChecklistsNavRecordedRoute:
      SignedinBusinessIdStoreIdNavChecklistsNavRecordedRoute,
    SignedinBusinessIdStoreIdNavChecklistsNavTemplatesRoute:
      SignedinBusinessIdStoreIdNavChecklistsNavTemplatesRoute,
    SignedinBusinessIdStoreIdNavChecklistsNavUpcomingRoute:
      SignedinBusinessIdStoreIdNavChecklistsNavUpcomingRoute,
    SignedinBusinessIdStoreIdNavChecklistsNavIndexRoute:
      SignedinBusinessIdStoreIdNavChecklistsNavIndexRoute,
  }

const SignedinBusinessIdStoreIdNavChecklistsNavRouteWithChildren =
  SignedinBusinessIdStoreIdNavChecklistsNavRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavChecklistsNavRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavChecklistsRouteChildren {
  SignedinBusinessIdStoreIdNavChecklistsNavRoute: typeof SignedinBusinessIdStoreIdNavChecklistsNavRouteWithChildren
  SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewRoute: typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewRoute
  SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexRoute: typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexRoute
  SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdRoute: typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdRoute
  SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseRoute: typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseRoute
  SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexRoute: typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexRoute
  SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdRoute: typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdRoute
}

const SignedinBusinessIdStoreIdNavChecklistsRouteChildren: SignedinBusinessIdStoreIdNavChecklistsRouteChildren =
  {
    SignedinBusinessIdStoreIdNavChecklistsNavRoute:
      SignedinBusinessIdStoreIdNavChecklistsNavRouteWithChildren,
    SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewRoute:
      SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewRoute,
    SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexRoute:
      SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexRoute,
    SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdRoute:
      SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdRoute,
    SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseRoute:
      SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseRoute,
    SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexRoute:
      SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexRoute,
    SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdRoute:
      SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdRoute,
  }

const SignedinBusinessIdStoreIdNavChecklistsRouteWithChildren =
  SignedinBusinessIdStoreIdNavChecklistsRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavChecklistsRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavSchedulesSettingsRouteChildren {
  SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityRoute
  SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesRoute
  SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsRoute
  SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffRoute
  SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsRoute
  SignedinBusinessIdStoreIdNavSchedulesSettingsIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsIndexRoute
}

const SignedinBusinessIdStoreIdNavSchedulesSettingsRouteChildren: SignedinBusinessIdStoreIdNavSchedulesSettingsRouteChildren =
  {
    SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityRoute:
      SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityRoute,
    SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesRoute:
      SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesRoute,
    SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsRoute:
      SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsRoute,
    SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffRoute:
      SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffRoute,
    SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsRoute:
      SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsRoute,
    SignedinBusinessIdStoreIdNavSchedulesSettingsIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesSettingsIndexRoute,
  }

const SignedinBusinessIdStoreIdNavSchedulesSettingsRouteWithChildren =
  SignedinBusinessIdStoreIdNavSchedulesSettingsRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavSchedulesSettingsRouteChildren,
  )

interface SignedinBusinessIdStoreIdNavRouteChildren {
  SignedinBusinessIdStoreIdNavEducationRoute: typeof SignedinBusinessIdStoreIdNavEducationRouteWithChildren
  SignedinBusinessIdStoreIdNavHrRoute: typeof SignedinBusinessIdStoreIdNavHrRouteWithChildren
  SignedinBusinessIdStoreIdNavPositionsRoute: typeof SignedinBusinessIdStoreIdNavPositionsRoute
  SignedinBusinessIdStoreIdNavSettingsRoute: typeof SignedinBusinessIdStoreIdNavSettingsRoute
  SignedinBusinessIdStoreIdNavStoreRoute: typeof SignedinBusinessIdStoreIdNavStoreRouteWithChildren
  SignedinBusinessIdStoreIdNavTeamRoute: typeof SignedinBusinessIdStoreIdNavTeamRouteWithChildren
  SignedinBusinessIdStoreIdNavTimePunchRoute: typeof SignedinBusinessIdStoreIdNavTimePunchRouteWithChildren
  SignedinBusinessIdStoreIdNavTrainingRoute: typeof SignedinBusinessIdStoreIdNavTrainingRouteWithChildren
  SignedinBusinessIdStoreIdNavWalkthroughRoute: typeof SignedinBusinessIdStoreIdNavWalkthroughRouteWithChildren
  SignedinBusinessIdStoreIdNavChecklistsRoute: typeof SignedinBusinessIdStoreIdNavChecklistsRouteWithChildren
  SignedinBusinessIdStoreIdNavDevicesClientIdRoute: typeof SignedinBusinessIdStoreIdNavDevicesClientIdRoute
  SignedinBusinessIdStoreIdNavSchedulesBuilderRoute: typeof SignedinBusinessIdStoreIdNavSchedulesBuilderRoute
  SignedinBusinessIdStoreIdNavSchedulesInsightsRoute: typeof SignedinBusinessIdStoreIdNavSchedulesInsightsRoute
  SignedinBusinessIdStoreIdNavSchedulesSettingsRoute: typeof SignedinBusinessIdStoreIdNavSchedulesSettingsRouteWithChildren
  SignedinBusinessIdStoreIdNavDevicesIndexRoute: typeof SignedinBusinessIdStoreIdNavDevicesIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsRoute: typeof SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsRoute
  SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdRoute
  SignedinBusinessIdStoreIdNavSchedulesDataUploadRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataUploadRoute
  SignedinBusinessIdStoreIdNavSchedulesDataIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesReportsIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesReportsIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateRoute
  SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdRoute
  SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexRoute: typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexRoute
}

const SignedinBusinessIdStoreIdNavRouteChildren: SignedinBusinessIdStoreIdNavRouteChildren =
  {
    SignedinBusinessIdStoreIdNavEducationRoute:
      SignedinBusinessIdStoreIdNavEducationRouteWithChildren,
    SignedinBusinessIdStoreIdNavHrRoute:
      SignedinBusinessIdStoreIdNavHrRouteWithChildren,
    SignedinBusinessIdStoreIdNavPositionsRoute:
      SignedinBusinessIdStoreIdNavPositionsRoute,
    SignedinBusinessIdStoreIdNavSettingsRoute:
      SignedinBusinessIdStoreIdNavSettingsRoute,
    SignedinBusinessIdStoreIdNavStoreRoute:
      SignedinBusinessIdStoreIdNavStoreRouteWithChildren,
    SignedinBusinessIdStoreIdNavTeamRoute:
      SignedinBusinessIdStoreIdNavTeamRouteWithChildren,
    SignedinBusinessIdStoreIdNavTimePunchRoute:
      SignedinBusinessIdStoreIdNavTimePunchRouteWithChildren,
    SignedinBusinessIdStoreIdNavTrainingRoute:
      SignedinBusinessIdStoreIdNavTrainingRouteWithChildren,
    SignedinBusinessIdStoreIdNavWalkthroughRoute:
      SignedinBusinessIdStoreIdNavWalkthroughRouteWithChildren,
    SignedinBusinessIdStoreIdNavChecklistsRoute:
      SignedinBusinessIdStoreIdNavChecklistsRouteWithChildren,
    SignedinBusinessIdStoreIdNavDevicesClientIdRoute:
      SignedinBusinessIdStoreIdNavDevicesClientIdRoute,
    SignedinBusinessIdStoreIdNavSchedulesBuilderRoute:
      SignedinBusinessIdStoreIdNavSchedulesBuilderRoute,
    SignedinBusinessIdStoreIdNavSchedulesInsightsRoute:
      SignedinBusinessIdStoreIdNavSchedulesInsightsRoute,
    SignedinBusinessIdStoreIdNavSchedulesSettingsRoute:
      SignedinBusinessIdStoreIdNavSchedulesSettingsRouteWithChildren,
    SignedinBusinessIdStoreIdNavDevicesIndexRoute:
      SignedinBusinessIdStoreIdNavDevicesIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsRoute:
      SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsRoute,
    SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdRoute:
      SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdRoute,
    SignedinBusinessIdStoreIdNavSchedulesDataUploadRoute:
      SignedinBusinessIdStoreIdNavSchedulesDataUploadRoute,
    SignedinBusinessIdStoreIdNavSchedulesDataIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesDataIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesReportsIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesReportsIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdRoute:
      SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateRoute,
    SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdRoute,
    SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexRoute:
      SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexRoute,
  }

const SignedinBusinessIdStoreIdNavRouteWithChildren =
  SignedinBusinessIdStoreIdNavRoute._addFileChildren(
    SignedinBusinessIdStoreIdNavRouteChildren,
  )

interface SignedinBusinessIdStoreIdRouteChildren {
  SignedinBusinessIdStoreIdNavRoute: typeof SignedinBusinessIdStoreIdNavRouteWithChildren
  SignedinBusinessIdStoreIdIndexRoute: typeof SignedinBusinessIdStoreIdIndexRoute
  SignedinBusinessIdStoreIdOnboardingCompleteRoute: typeof SignedinBusinessIdStoreIdOnboardingCompleteRoute
  SignedinBusinessIdStoreIdOnboardingLaborLawsRoute: typeof SignedinBusinessIdStoreIdOnboardingLaborLawsRoute
  SignedinBusinessIdStoreIdOnboardingRequiredBreaksRoute: typeof SignedinBusinessIdStoreIdOnboardingRequiredBreaksRoute
  SignedinBusinessIdStoreIdReportsAvailabilityRoute: typeof SignedinBusinessIdStoreIdReportsAvailabilityRoute
  SignedinBusinessIdStoreIdReportsHourlySalesRoute: typeof SignedinBusinessIdStoreIdReportsHourlySalesRoute
  SignedinBusinessIdStoreIdOnboardingIndexRoute: typeof SignedinBusinessIdStoreIdOnboardingIndexRoute
  SignedinBusinessIdStoreIdPrintScheduleIdDailyRoute: typeof SignedinBusinessIdStoreIdPrintScheduleIdDailyRoute
  SignedinBusinessIdStoreIdPrintScheduleIdTeamRoute: typeof SignedinBusinessIdStoreIdPrintScheduleIdTeamRoute
  SignedinBusinessIdStoreIdSchedulesScheduleIdIndexRoute: typeof SignedinBusinessIdStoreIdSchedulesScheduleIdIndexRoute
  SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsRoute: typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsRoute
  SignedinBusinessIdStoreIdPrintInsightsSchedulingTableRoute: typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingTableRoute
}

const SignedinBusinessIdStoreIdRouteChildren: SignedinBusinessIdStoreIdRouteChildren =
  {
    SignedinBusinessIdStoreIdNavRoute:
      SignedinBusinessIdStoreIdNavRouteWithChildren,
    SignedinBusinessIdStoreIdIndexRoute: SignedinBusinessIdStoreIdIndexRoute,
    SignedinBusinessIdStoreIdOnboardingCompleteRoute:
      SignedinBusinessIdStoreIdOnboardingCompleteRoute,
    SignedinBusinessIdStoreIdOnboardingLaborLawsRoute:
      SignedinBusinessIdStoreIdOnboardingLaborLawsRoute,
    SignedinBusinessIdStoreIdOnboardingRequiredBreaksRoute:
      SignedinBusinessIdStoreIdOnboardingRequiredBreaksRoute,
    SignedinBusinessIdStoreIdReportsAvailabilityRoute:
      SignedinBusinessIdStoreIdReportsAvailabilityRoute,
    SignedinBusinessIdStoreIdReportsHourlySalesRoute:
      SignedinBusinessIdStoreIdReportsHourlySalesRoute,
    SignedinBusinessIdStoreIdOnboardingIndexRoute:
      SignedinBusinessIdStoreIdOnboardingIndexRoute,
    SignedinBusinessIdStoreIdPrintScheduleIdDailyRoute:
      SignedinBusinessIdStoreIdPrintScheduleIdDailyRoute,
    SignedinBusinessIdStoreIdPrintScheduleIdTeamRoute:
      SignedinBusinessIdStoreIdPrintScheduleIdTeamRoute,
    SignedinBusinessIdStoreIdSchedulesScheduleIdIndexRoute:
      SignedinBusinessIdStoreIdSchedulesScheduleIdIndexRoute,
    SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsRoute:
      SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsRoute,
    SignedinBusinessIdStoreIdPrintInsightsSchedulingTableRoute:
      SignedinBusinessIdStoreIdPrintInsightsSchedulingTableRoute,
  }

const SignedinBusinessIdStoreIdRouteWithChildren =
  SignedinBusinessIdStoreIdRoute._addFileChildren(
    SignedinBusinessIdStoreIdRouteChildren,
  )

interface SignedinRouteChildren {
  SignedinAdminRoute: typeof SignedinAdminRouteWithChildren
  SignedinBusinessIdStoreIdRoute: typeof SignedinBusinessIdStoreIdRouteWithChildren
}

const SignedinRouteChildren: SignedinRouteChildren = {
  SignedinAdminRoute: SignedinAdminRouteWithChildren,
  SignedinBusinessIdStoreIdRoute: SignedinBusinessIdStoreIdRouteWithChildren,
}

const SignedinRouteWithChildren = SignedinRoute._addFileChildren(
  SignedinRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof SignedinRouteWithChildren
  '/forgot-password': typeof ForgotPasswordRoute
  '/forgot-password-code': typeof ForgotPasswordCodeRoute
  '/verify-email': typeof VerifyEmailRoute
  '/admin': typeof SignedinAdminRouteWithChildren
  '/import': typeof ImportIndexRoute
  '/admin/dashboard': typeof SignedinAdminDashboardRoute
  '/$businessId/$storeId': typeof SignedinBusinessIdStoreIdNavRouteWithChildren
  '/admin/businesses/create': typeof SignedinAdminBusinessesCreateRoute
  '/admin/education/$id': typeof SignedinAdminEducationIdRoute
  '/admin/education/create': typeof SignedinAdminEducationCreateRoute
  '/admin/invitations/create': typeof SignedinAdminInvitationsCreateRoute
  '/admin/newspaper/visibility': typeof SignedinAdminNewspaperVisibilityRoute
  '/admin/users/create': typeof SignedinAdminUsersCreateRoute
  '/$businessId/$storeId/': typeof SignedinBusinessIdStoreIdIndexRoute
  '/admin/businesses': typeof SignedinAdminBusinessesIndexRoute
  '/admin/education': typeof SignedinAdminEducationIndexRoute
  '/admin/features': typeof SignedinAdminFeaturesIndexRoute
  '/admin/invitations': typeof SignedinAdminInvitationsIndexRoute
  '/admin/newspaper': typeof SignedinAdminNewspaperIndexRoute
  '/admin/onboarding': typeof SignedinAdminOnboardingIndexRoute
  '/admin/people': typeof SignedinAdminPeopleIndexRoute
  '/admin/stores': typeof SignedinAdminStoresIndexRoute
  '/admin/users': typeof SignedinAdminUsersIndexRoute
  '/import/store/$id': typeof ImportStoreIdIndexRoute
  '/$businessId/$storeId/education': typeof SignedinBusinessIdStoreIdNavEducationRouteWithChildren
  '/$businessId/$storeId/hr': typeof SignedinBusinessIdStoreIdNavHrRouteWithChildren
  '/$businessId/$storeId/positions': typeof SignedinBusinessIdStoreIdNavPositionsRoute
  '/$businessId/$storeId/settings': typeof SignedinBusinessIdStoreIdNavSettingsRoute
  '/$businessId/$storeId/store': typeof SignedinBusinessIdStoreIdNavStoreRouteWithChildren
  '/$businessId/$storeId/team': typeof SignedinBusinessIdStoreIdNavTeamRouteWithChildren
  '/$businessId/$storeId/timePunch': typeof SignedinBusinessIdStoreIdNavTimePunchRouteWithChildren
  '/$businessId/$storeId/training': typeof SignedinBusinessIdStoreIdNavTrainingRouteWithChildren
  '/$businessId/$storeId/walkthrough': typeof SignedinBusinessIdStoreIdNavWalkthroughRouteWithChildren
  '/$businessId/$storeId/onboarding/complete': typeof SignedinBusinessIdStoreIdOnboardingCompleteRoute
  '/$businessId/$storeId/onboarding/laborLaws': typeof SignedinBusinessIdStoreIdOnboardingLaborLawsRoute
  '/$businessId/$storeId/onboarding/requiredBreaks': typeof SignedinBusinessIdStoreIdOnboardingRequiredBreaksRoute
  '/$businessId/$storeId/reports/availability': typeof SignedinBusinessIdStoreIdReportsAvailabilityRoute
  '/$businessId/$storeId/reports/hourly-sales': typeof SignedinBusinessIdStoreIdReportsHourlySalesRoute
  '/admin/businesses/$businessId/invite': typeof SignedinAdminBusinessesBusinessIdInviteRoute
  '/admin/people/$personId/edit': typeof SignedinAdminPeoplePersonIdEditRoute
  '/admin/people/$personId/streamchat': typeof SignedinAdminPeoplePersonIdStreamchatRoute
  '/admin/users/$userId/edit': typeof SignedinAdminUsersUserIdEditRoute
  '/$businessId/$storeId/onboarding': typeof SignedinBusinessIdStoreIdOnboardingIndexRoute
  '/admin/businesses/$businessId': typeof SignedinAdminBusinessesBusinessIdIndexRoute
  '/$businessId/$storeId/checklists': typeof SignedinBusinessIdStoreIdNavChecklistsNavRouteWithChildren
  '/$businessId/$storeId/devices/$clientId': typeof SignedinBusinessIdStoreIdNavDevicesClientIdRoute
  '/$businessId/$storeId/education/$id': typeof SignedinBusinessIdStoreIdNavEducationIdRoute
  '/$businessId/$storeId/schedules/builder': typeof SignedinBusinessIdStoreIdNavSchedulesBuilderRoute
  '/$businessId/$storeId/schedules/insights': typeof SignedinBusinessIdStoreIdNavSchedulesInsightsRoute
  '/$businessId/$storeId/schedules/settings': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsRouteWithChildren
  '/$businessId/$storeId/store/settings': typeof SignedinBusinessIdStoreIdNavStoreSettingsRouteWithChildren
  '/$businessId/$storeId/walkthrough/$step': typeof SignedinBusinessIdStoreIdNavWalkthroughStepRoute
  '/$businessId/$storeId/walkthrough/team-member-upload': typeof SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadRoute
  '/$businessId/$storeId/print/$scheduleId/daily': typeof SignedinBusinessIdStoreIdPrintScheduleIdDailyRoute
  '/$businessId/$storeId/print/$scheduleId/team': typeof SignedinBusinessIdStoreIdPrintScheduleIdTeamRoute
  '/admin/businesses/$businessId/stores/create': typeof SignedinAdminBusinessesBusinessIdStoresCreateRoute
  '/admin/newspaper/paper/$newspaperId/edit': typeof SignedinAdminNewspaperPaperNewspaperIdEditRoute
  '/admin/newspaper/visibilityGroup/$visibilityGroupId/edit': typeof SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditRoute
  '/$businessId/$storeId/devices': typeof SignedinBusinessIdStoreIdNavDevicesIndexRoute
  '/$businessId/$storeId/education/': typeof SignedinBusinessIdStoreIdNavEducationIndexRoute
  '/$businessId/$storeId/schedules': typeof SignedinBusinessIdStoreIdNavSchedulesIndexRoute
  '/$businessId/$storeId/walkthrough/': typeof SignedinBusinessIdStoreIdNavWalkthroughIndexRoute
  '/$businessId/$storeId/schedules/$scheduleId': typeof SignedinBusinessIdStoreIdSchedulesScheduleIdIndexRoute
  '/$businessId/$storeId/checklists/$checklistId/view': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewRoute
  '/$businessId/$storeId/checklists/active': typeof SignedinBusinessIdStoreIdNavChecklistsNavActiveRoute
  '/$businessId/$storeId/checklists/recorded': typeof SignedinBusinessIdStoreIdNavChecklistsNavRecordedRoute
  '/$businessId/$storeId/checklists/templates': typeof SignedinBusinessIdStoreIdNavChecklistsNavTemplatesRoute
  '/$businessId/$storeId/checklists/upcoming': typeof SignedinBusinessIdStoreIdNavChecklistsNavUpcomingRoute
  '/$businessId/$storeId/hr/documents/edit': typeof SignedinBusinessIdStoreIdNavHrDocumentsEditRoute
  '/$businessId/$storeId/hr/documents/view': typeof SignedinBusinessIdStoreIdNavHrDocumentsViewRoute
  '/$businessId/$storeId/hr/reminders/edit': typeof SignedinBusinessIdStoreIdNavHrRemindersEditRoute
  '/$businessId/$storeId/hr/reminders/view': typeof SignedinBusinessIdStoreIdNavHrRemindersViewRoute
  '/$businessId/$storeId/schedules/$scheduleId/metrics': typeof SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsRoute
  '/$businessId/$storeId/schedules/data/$dataFileId': typeof SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdRoute
  '/$businessId/$storeId/schedules/data/upload': typeof SignedinBusinessIdStoreIdNavSchedulesDataUploadRoute
  '/$businessId/$storeId/schedules/settings/availability': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityRoute
  '/$businessId/$storeId/schedules/settings/break-rules': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesRoute
  '/$businessId/$storeId/schedules/settings/labor-laws': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsRoute
  '/$businessId/$storeId/schedules/settings/time-off': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffRoute
  '/$businessId/$storeId/schedules/settings/validations': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsRoute
  '/$businessId/$storeId/store/announcements/edit': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsEditRoute
  '/$businessId/$storeId/store/announcements/view': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsViewRoute
  '/$businessId/$storeId/store/links/$link': typeof SignedinBusinessIdStoreIdNavStoreLinksLinkRoute
  '/$businessId/$storeId/store/settings/core-values': typeof SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesRoute
  '/$businessId/$storeId/store/settings/store-info': typeof SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoRoute
  '/$businessId/$storeId/store/settings/work-mode': typeof SignedinBusinessIdStoreIdNavStoreSettingsWorkModeRoute
  '/$businessId/$storeId/team/directory/$personId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteWithChildren
  '/$businessId/$storeId/print/insights/scheduling/shifts': typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsRoute
  '/$businessId/$storeId/print/insights/scheduling/table': typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingTableRoute
  '/$businessId/$storeId/checklists/$checklistId': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexRoute
  '/$businessId/$storeId/checklists/': typeof SignedinBusinessIdStoreIdNavChecklistsNavIndexRoute
  '/$businessId/$storeId/hr/documents': typeof SignedinBusinessIdStoreIdNavHrDocumentsIndexRoute
  '/$businessId/$storeId/hr/insights': typeof SignedinBusinessIdStoreIdNavHrInsightsIndexRoute
  '/$businessId/$storeId/hr/reminders': typeof SignedinBusinessIdStoreIdNavHrRemindersIndexRoute
  '/$businessId/$storeId/schedules/data': typeof SignedinBusinessIdStoreIdNavSchedulesDataIndexRoute
  '/$businessId/$storeId/schedules/reports': typeof SignedinBusinessIdStoreIdNavSchedulesReportsIndexRoute
  '/$businessId/$storeId/schedules/requests': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsIndexRoute
  '/$businessId/$storeId/schedules/settings/': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsIndexRoute
  '/$businessId/$storeId/store/announcements': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexRoute
  '/$businessId/$storeId/store/events': typeof SignedinBusinessIdStoreIdNavStoreEventsIndexRoute
  '/$businessId/$storeId/store/files': typeof SignedinBusinessIdStoreIdNavStoreFilesIndexRoute
  '/$businessId/$storeId/store/links': typeof SignedinBusinessIdStoreIdNavStoreLinksIndexRoute
  '/$businessId/$storeId/team/directory': typeof SignedinBusinessIdStoreIdNavTeamDirectoryIndexRoute
  '/$businessId/$storeId/team/reports': typeof SignedinBusinessIdStoreIdNavTeamReportsIndexRoute
  '/$businessId/$storeId/timePunch/punches': typeof SignedinBusinessIdStoreIdNavTimePunchPunchesIndexRoute
  '/$businessId/$storeId/timePunch/teamMemberVariance': typeof SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexRoute
  '/$businessId/$storeId/timePunch/teamTotals': typeof SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexRoute
  '/$businessId/$storeId/timePunch/variance': typeof SignedinBusinessIdStoreIdNavTimePunchVarianceIndexRoute
  '/$businessId/$storeId/training/insights': typeof SignedinBusinessIdStoreIdNavTrainingInsightsIndexRoute
  '/admin/businesses/$businessId/stores/$storeId': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdIndexRoute
  '/$businessId/$storeId/checklists/$checklistId/item/$itemId': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdRoute
  '/$businessId/$storeId/checklists/templates/$templateId/use': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseRoute
  '/$businessId/$storeId/schedules/data/forecasts/$forecastJobId': typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdRoute
  '/$businessId/$storeId/schedules/requests/availability/$personAvailabilityId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdRoute
  '/$businessId/$storeId/schedules/requests/shiftOffers/$shiftOfferId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdRoute
  '/$businessId/$storeId/schedules/requests/timeOff/$timeOffId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdRoute
  '/$businessId/$storeId/schedules/requests/timeOff/create': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateRoute
  '/$businessId/$storeId/store/files/$fileId/view': typeof SignedinBusinessIdStoreIdNavStoreFilesFileIdViewRoute
  '/$businessId/$storeId/store/settings/area/$areaId': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteWithChildren
  '/$businessId/$storeId/store/settings/area/work-areas': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasRoute
  '/$businessId/$storeId/team/directory/$personId/about': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteWithChildren
  '/$businessId/$storeId/team/directory/$personId/documents': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsRoute
  '/$businessId/$storeId/team/directory/$personId/employment': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteWithChildren
  '/$businessId/$storeId/team/directory/$personId/notes': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteWithChildren
  '/$businessId/$storeId/team/directory/$personId/positions': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsRoute
  '/admin/businesses/$businessId/stores/$storeId/checklists/import': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportRoute
  '/admin/businesses/$businessId/stores/$storeId/person/$personId': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdRoute
  '/$businessId/$storeId/checklists/templates/$templateId': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexRoute
  '/$businessId/$storeId/schedules/data/files': typeof SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexRoute
  '/$businessId/$storeId/schedules/data/forecasts': typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexRoute
  '/$businessId/$storeId/schedules/requests/availability': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexRoute
  '/$businessId/$storeId/schedules/requests/shiftOffers': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexRoute
  '/$businessId/$storeId/schedules/requests/timeOff': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexRoute
  '/$businessId/$storeId/store/settings/job-titles': typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexRoute
  '/admin/businesses/$businessId/stores/$storeId/checklists': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexRoute
  '/$businessId/$storeId/checklists/templates/$templateId/item/$itemId': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdRoute
  '/$businessId/$storeId/schedules/requests/availability/edit/$personAvailabilityId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdRoute
  '/$businessId/$storeId/schedules/requests/timeOff/edit/$timeOffId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdRoute
  '/$businessId/$storeId/store/settings/area/$areaId/edit': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditRoute
  '/$businessId/$storeId/store/settings/area/$areaId/positions': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsRoute
  '/$businessId/$storeId/store/settings/job-titles/$jobId/edit': typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditRoute
  '/$businessId/$storeId/team/directory/$personId/about/contact-details': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsRoute
  '/$businessId/$storeId/team/directory/$personId/about/personal': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalRoute
  '/$businessId/$storeId/team/directory/$personId/employment/info': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoRoute
  '/$businessId/$storeId/team/directory/$personId/employment/permissions': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsRoute
  '/$businessId/$storeId/team/directory/$personId/employment/wages': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesRoute
  '/$businessId/$storeId/schedules/requests/availability/edit': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexRoute
  '/$businessId/$storeId/team/directory/$personId/notes/': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexRoute
  '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/edit': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditRoute
  '/$businessId/$storeId/team/directory/$personId/notes/note/$noteId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdRoute
  '/$businessId/$storeId/team/directory/$personId/notes/note/edit': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditRoute
  '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeRoute
  '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewRoute
  '/$businessId/$storeId/team/directory/$personId/notes/note/cmFromAi/$actionableItemId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof SignedinRouteWithChildren
  '/forgot-password': typeof ForgotPasswordRoute
  '/forgot-password-code': typeof ForgotPasswordCodeRoute
  '/verify-email': typeof VerifyEmailRoute
  '/admin': typeof SignedinAdminRouteWithChildren
  '/import': typeof ImportIndexRoute
  '/admin/dashboard': typeof SignedinAdminDashboardRoute
  '/$businessId/$storeId': typeof SignedinBusinessIdStoreIdIndexRoute
  '/admin/businesses/create': typeof SignedinAdminBusinessesCreateRoute
  '/admin/education/$id': typeof SignedinAdminEducationIdRoute
  '/admin/education/create': typeof SignedinAdminEducationCreateRoute
  '/admin/invitations/create': typeof SignedinAdminInvitationsCreateRoute
  '/admin/newspaper/visibility': typeof SignedinAdminNewspaperVisibilityRoute
  '/admin/users/create': typeof SignedinAdminUsersCreateRoute
  '/admin/businesses': typeof SignedinAdminBusinessesIndexRoute
  '/admin/education': typeof SignedinAdminEducationIndexRoute
  '/admin/features': typeof SignedinAdminFeaturesIndexRoute
  '/admin/invitations': typeof SignedinAdminInvitationsIndexRoute
  '/admin/newspaper': typeof SignedinAdminNewspaperIndexRoute
  '/admin/onboarding': typeof SignedinAdminOnboardingIndexRoute
  '/admin/people': typeof SignedinAdminPeopleIndexRoute
  '/admin/stores': typeof SignedinAdminStoresIndexRoute
  '/admin/users': typeof SignedinAdminUsersIndexRoute
  '/import/store/$id': typeof ImportStoreIdIndexRoute
  '/$businessId/$storeId/hr': typeof SignedinBusinessIdStoreIdNavHrRouteWithChildren
  '/$businessId/$storeId/positions': typeof SignedinBusinessIdStoreIdNavPositionsRoute
  '/$businessId/$storeId/settings': typeof SignedinBusinessIdStoreIdNavSettingsRoute
  '/$businessId/$storeId/store': typeof SignedinBusinessIdStoreIdNavStoreRouteWithChildren
  '/$businessId/$storeId/team': typeof SignedinBusinessIdStoreIdNavTeamRouteWithChildren
  '/$businessId/$storeId/timePunch': typeof SignedinBusinessIdStoreIdNavTimePunchRouteWithChildren
  '/$businessId/$storeId/training': typeof SignedinBusinessIdStoreIdNavTrainingRouteWithChildren
  '/$businessId/$storeId/onboarding/complete': typeof SignedinBusinessIdStoreIdOnboardingCompleteRoute
  '/$businessId/$storeId/onboarding/laborLaws': typeof SignedinBusinessIdStoreIdOnboardingLaborLawsRoute
  '/$businessId/$storeId/onboarding/requiredBreaks': typeof SignedinBusinessIdStoreIdOnboardingRequiredBreaksRoute
  '/$businessId/$storeId/reports/availability': typeof SignedinBusinessIdStoreIdReportsAvailabilityRoute
  '/$businessId/$storeId/reports/hourly-sales': typeof SignedinBusinessIdStoreIdReportsHourlySalesRoute
  '/admin/businesses/$businessId/invite': typeof SignedinAdminBusinessesBusinessIdInviteRoute
  '/admin/people/$personId/edit': typeof SignedinAdminPeoplePersonIdEditRoute
  '/admin/people/$personId/streamchat': typeof SignedinAdminPeoplePersonIdStreamchatRoute
  '/admin/users/$userId/edit': typeof SignedinAdminUsersUserIdEditRoute
  '/$businessId/$storeId/onboarding': typeof SignedinBusinessIdStoreIdOnboardingIndexRoute
  '/admin/businesses/$businessId': typeof SignedinAdminBusinessesBusinessIdIndexRoute
  '/$businessId/$storeId/checklists': typeof SignedinBusinessIdStoreIdNavChecklistsNavIndexRoute
  '/$businessId/$storeId/devices/$clientId': typeof SignedinBusinessIdStoreIdNavDevicesClientIdRoute
  '/$businessId/$storeId/education/$id': typeof SignedinBusinessIdStoreIdNavEducationIdRoute
  '/$businessId/$storeId/schedules/builder': typeof SignedinBusinessIdStoreIdNavSchedulesBuilderRoute
  '/$businessId/$storeId/schedules/insights': typeof SignedinBusinessIdStoreIdNavSchedulesInsightsRoute
  '/$businessId/$storeId/store/settings': typeof SignedinBusinessIdStoreIdNavStoreSettingsRouteWithChildren
  '/$businessId/$storeId/walkthrough/$step': typeof SignedinBusinessIdStoreIdNavWalkthroughStepRoute
  '/$businessId/$storeId/walkthrough/team-member-upload': typeof SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadRoute
  '/$businessId/$storeId/print/$scheduleId/daily': typeof SignedinBusinessIdStoreIdPrintScheduleIdDailyRoute
  '/$businessId/$storeId/print/$scheduleId/team': typeof SignedinBusinessIdStoreIdPrintScheduleIdTeamRoute
  '/admin/businesses/$businessId/stores/create': typeof SignedinAdminBusinessesBusinessIdStoresCreateRoute
  '/admin/newspaper/paper/$newspaperId/edit': typeof SignedinAdminNewspaperPaperNewspaperIdEditRoute
  '/admin/newspaper/visibilityGroup/$visibilityGroupId/edit': typeof SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditRoute
  '/$businessId/$storeId/devices': typeof SignedinBusinessIdStoreIdNavDevicesIndexRoute
  '/$businessId/$storeId/education': typeof SignedinBusinessIdStoreIdNavEducationIndexRoute
  '/$businessId/$storeId/schedules': typeof SignedinBusinessIdStoreIdNavSchedulesIndexRoute
  '/$businessId/$storeId/walkthrough': typeof SignedinBusinessIdStoreIdNavWalkthroughIndexRoute
  '/$businessId/$storeId/schedules/$scheduleId': typeof SignedinBusinessIdStoreIdSchedulesScheduleIdIndexRoute
  '/$businessId/$storeId/checklists/$checklistId/view': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewRoute
  '/$businessId/$storeId/checklists/active': typeof SignedinBusinessIdStoreIdNavChecklistsNavActiveRoute
  '/$businessId/$storeId/checklists/recorded': typeof SignedinBusinessIdStoreIdNavChecklistsNavRecordedRoute
  '/$businessId/$storeId/checklists/templates': typeof SignedinBusinessIdStoreIdNavChecklistsNavTemplatesRoute
  '/$businessId/$storeId/checklists/upcoming': typeof SignedinBusinessIdStoreIdNavChecklistsNavUpcomingRoute
  '/$businessId/$storeId/hr/documents/edit': typeof SignedinBusinessIdStoreIdNavHrDocumentsEditRoute
  '/$businessId/$storeId/hr/documents/view': typeof SignedinBusinessIdStoreIdNavHrDocumentsViewRoute
  '/$businessId/$storeId/hr/reminders/edit': typeof SignedinBusinessIdStoreIdNavHrRemindersEditRoute
  '/$businessId/$storeId/hr/reminders/view': typeof SignedinBusinessIdStoreIdNavHrRemindersViewRoute
  '/$businessId/$storeId/schedules/$scheduleId/metrics': typeof SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsRoute
  '/$businessId/$storeId/schedules/data/$dataFileId': typeof SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdRoute
  '/$businessId/$storeId/schedules/data/upload': typeof SignedinBusinessIdStoreIdNavSchedulesDataUploadRoute
  '/$businessId/$storeId/schedules/settings/availability': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityRoute
  '/$businessId/$storeId/schedules/settings/break-rules': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesRoute
  '/$businessId/$storeId/schedules/settings/labor-laws': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsRoute
  '/$businessId/$storeId/schedules/settings/time-off': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffRoute
  '/$businessId/$storeId/schedules/settings/validations': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsRoute
  '/$businessId/$storeId/store/announcements/edit': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsEditRoute
  '/$businessId/$storeId/store/announcements/view': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsViewRoute
  '/$businessId/$storeId/store/links/$link': typeof SignedinBusinessIdStoreIdNavStoreLinksLinkRoute
  '/$businessId/$storeId/store/settings/core-values': typeof SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesRoute
  '/$businessId/$storeId/store/settings/store-info': typeof SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoRoute
  '/$businessId/$storeId/store/settings/work-mode': typeof SignedinBusinessIdStoreIdNavStoreSettingsWorkModeRoute
  '/$businessId/$storeId/team/directory/$personId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteWithChildren
  '/$businessId/$storeId/print/insights/scheduling/shifts': typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsRoute
  '/$businessId/$storeId/print/insights/scheduling/table': typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingTableRoute
  '/$businessId/$storeId/checklists/$checklistId': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexRoute
  '/$businessId/$storeId/hr/documents': typeof SignedinBusinessIdStoreIdNavHrDocumentsIndexRoute
  '/$businessId/$storeId/hr/insights': typeof SignedinBusinessIdStoreIdNavHrInsightsIndexRoute
  '/$businessId/$storeId/hr/reminders': typeof SignedinBusinessIdStoreIdNavHrRemindersIndexRoute
  '/$businessId/$storeId/schedules/data': typeof SignedinBusinessIdStoreIdNavSchedulesDataIndexRoute
  '/$businessId/$storeId/schedules/reports': typeof SignedinBusinessIdStoreIdNavSchedulesReportsIndexRoute
  '/$businessId/$storeId/schedules/requests': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsIndexRoute
  '/$businessId/$storeId/schedules/settings': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsIndexRoute
  '/$businessId/$storeId/store/announcements': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexRoute
  '/$businessId/$storeId/store/events': typeof SignedinBusinessIdStoreIdNavStoreEventsIndexRoute
  '/$businessId/$storeId/store/files': typeof SignedinBusinessIdStoreIdNavStoreFilesIndexRoute
  '/$businessId/$storeId/store/links': typeof SignedinBusinessIdStoreIdNavStoreLinksIndexRoute
  '/$businessId/$storeId/team/directory': typeof SignedinBusinessIdStoreIdNavTeamDirectoryIndexRoute
  '/$businessId/$storeId/team/reports': typeof SignedinBusinessIdStoreIdNavTeamReportsIndexRoute
  '/$businessId/$storeId/timePunch/punches': typeof SignedinBusinessIdStoreIdNavTimePunchPunchesIndexRoute
  '/$businessId/$storeId/timePunch/teamMemberVariance': typeof SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexRoute
  '/$businessId/$storeId/timePunch/teamTotals': typeof SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexRoute
  '/$businessId/$storeId/timePunch/variance': typeof SignedinBusinessIdStoreIdNavTimePunchVarianceIndexRoute
  '/$businessId/$storeId/training/insights': typeof SignedinBusinessIdStoreIdNavTrainingInsightsIndexRoute
  '/admin/businesses/$businessId/stores/$storeId': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdIndexRoute
  '/$businessId/$storeId/checklists/$checklistId/item/$itemId': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdRoute
  '/$businessId/$storeId/checklists/templates/$templateId/use': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseRoute
  '/$businessId/$storeId/schedules/data/forecasts/$forecastJobId': typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdRoute
  '/$businessId/$storeId/schedules/requests/availability/$personAvailabilityId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdRoute
  '/$businessId/$storeId/schedules/requests/shiftOffers/$shiftOfferId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdRoute
  '/$businessId/$storeId/schedules/requests/timeOff/$timeOffId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdRoute
  '/$businessId/$storeId/schedules/requests/timeOff/create': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateRoute
  '/$businessId/$storeId/store/files/$fileId/view': typeof SignedinBusinessIdStoreIdNavStoreFilesFileIdViewRoute
  '/$businessId/$storeId/store/settings/area/$areaId': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteWithChildren
  '/$businessId/$storeId/store/settings/area/work-areas': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasRoute
  '/$businessId/$storeId/team/directory/$personId/about': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteWithChildren
  '/$businessId/$storeId/team/directory/$personId/documents': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsRoute
  '/$businessId/$storeId/team/directory/$personId/employment': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteWithChildren
  '/$businessId/$storeId/team/directory/$personId/positions': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsRoute
  '/admin/businesses/$businessId/stores/$storeId/checklists/import': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportRoute
  '/admin/businesses/$businessId/stores/$storeId/person/$personId': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdRoute
  '/$businessId/$storeId/checklists/templates/$templateId': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexRoute
  '/$businessId/$storeId/schedules/data/files': typeof SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexRoute
  '/$businessId/$storeId/schedules/data/forecasts': typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexRoute
  '/$businessId/$storeId/schedules/requests/availability': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexRoute
  '/$businessId/$storeId/schedules/requests/shiftOffers': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexRoute
  '/$businessId/$storeId/schedules/requests/timeOff': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexRoute
  '/$businessId/$storeId/store/settings/job-titles': typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexRoute
  '/admin/businesses/$businessId/stores/$storeId/checklists': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexRoute
  '/$businessId/$storeId/checklists/templates/$templateId/item/$itemId': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdRoute
  '/$businessId/$storeId/schedules/requests/availability/edit/$personAvailabilityId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdRoute
  '/$businessId/$storeId/schedules/requests/timeOff/edit/$timeOffId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdRoute
  '/$businessId/$storeId/store/settings/area/$areaId/edit': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditRoute
  '/$businessId/$storeId/store/settings/area/$areaId/positions': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsRoute
  '/$businessId/$storeId/store/settings/job-titles/$jobId/edit': typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditRoute
  '/$businessId/$storeId/team/directory/$personId/about/contact-details': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsRoute
  '/$businessId/$storeId/team/directory/$personId/about/personal': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalRoute
  '/$businessId/$storeId/team/directory/$personId/employment/info': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoRoute
  '/$businessId/$storeId/team/directory/$personId/employment/permissions': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsRoute
  '/$businessId/$storeId/team/directory/$personId/employment/wages': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesRoute
  '/$businessId/$storeId/schedules/requests/availability/edit': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexRoute
  '/$businessId/$storeId/team/directory/$personId/notes': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexRoute
  '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/edit': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditRoute
  '/$businessId/$storeId/team/directory/$personId/notes/note/$noteId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdRoute
  '/$businessId/$storeId/team/directory/$personId/notes/note/edit': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditRoute
  '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeRoute
  '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewRoute
  '/$businessId/$storeId/team/directory/$personId/notes/note/cmFromAi/$actionableItemId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_signedin': typeof SignedinRouteWithChildren
  '/forgot-password': typeof ForgotPasswordRoute
  '/forgot-password-code': typeof ForgotPasswordCodeRoute
  '/verify-email': typeof VerifyEmailRoute
  '/_signedin/admin': typeof SignedinAdminRouteWithChildren
  '/import/': typeof ImportIndexRoute
  '/_signedin/admin/dashboard': typeof SignedinAdminDashboardRoute
  '/_signedin/$businessId/$storeId': typeof SignedinBusinessIdStoreIdRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav': typeof SignedinBusinessIdStoreIdNavRouteWithChildren
  '/_signedin/admin/businesses/create': typeof SignedinAdminBusinessesCreateRoute
  '/_signedin/admin/education/$id': typeof SignedinAdminEducationIdRoute
  '/_signedin/admin/education/create': typeof SignedinAdminEducationCreateRoute
  '/_signedin/admin/invitations/create': typeof SignedinAdminInvitationsCreateRoute
  '/_signedin/admin/newspaper/visibility': typeof SignedinAdminNewspaperVisibilityRoute
  '/_signedin/admin/users/create': typeof SignedinAdminUsersCreateRoute
  '/_signedin/$businessId/$storeId/': typeof SignedinBusinessIdStoreIdIndexRoute
  '/_signedin/admin/businesses/': typeof SignedinAdminBusinessesIndexRoute
  '/_signedin/admin/education/': typeof SignedinAdminEducationIndexRoute
  '/_signedin/admin/features/': typeof SignedinAdminFeaturesIndexRoute
  '/_signedin/admin/invitations/': typeof SignedinAdminInvitationsIndexRoute
  '/_signedin/admin/newspaper/': typeof SignedinAdminNewspaperIndexRoute
  '/_signedin/admin/onboarding/': typeof SignedinAdminOnboardingIndexRoute
  '/_signedin/admin/people/': typeof SignedinAdminPeopleIndexRoute
  '/_signedin/admin/stores/': typeof SignedinAdminStoresIndexRoute
  '/_signedin/admin/users/': typeof SignedinAdminUsersIndexRoute
  '/import/store/$id/': typeof ImportStoreIdIndexRoute
  '/_signedin/$businessId/$storeId/_nav/education': typeof SignedinBusinessIdStoreIdNavEducationRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/hr': typeof SignedinBusinessIdStoreIdNavHrRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/positions': typeof SignedinBusinessIdStoreIdNavPositionsRoute
  '/_signedin/$businessId/$storeId/_nav/settings': typeof SignedinBusinessIdStoreIdNavSettingsRoute
  '/_signedin/$businessId/$storeId/_nav/store': typeof SignedinBusinessIdStoreIdNavStoreRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/team': typeof SignedinBusinessIdStoreIdNavTeamRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/timePunch': typeof SignedinBusinessIdStoreIdNavTimePunchRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/training': typeof SignedinBusinessIdStoreIdNavTrainingRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/walkthrough': typeof SignedinBusinessIdStoreIdNavWalkthroughRouteWithChildren
  '/_signedin/$businessId/$storeId/onboarding/complete': typeof SignedinBusinessIdStoreIdOnboardingCompleteRoute
  '/_signedin/$businessId/$storeId/onboarding/laborLaws': typeof SignedinBusinessIdStoreIdOnboardingLaborLawsRoute
  '/_signedin/$businessId/$storeId/onboarding/requiredBreaks': typeof SignedinBusinessIdStoreIdOnboardingRequiredBreaksRoute
  '/_signedin/$businessId/$storeId/reports/availability': typeof SignedinBusinessIdStoreIdReportsAvailabilityRoute
  '/_signedin/$businessId/$storeId/reports/hourly-sales': typeof SignedinBusinessIdStoreIdReportsHourlySalesRoute
  '/_signedin/admin/businesses/$businessId/invite': typeof SignedinAdminBusinessesBusinessIdInviteRoute
  '/_signedin/admin/people/$personId/edit': typeof SignedinAdminPeoplePersonIdEditRoute
  '/_signedin/admin/people/$personId/streamchat': typeof SignedinAdminPeoplePersonIdStreamchatRoute
  '/_signedin/admin/users/$userId/edit': typeof SignedinAdminUsersUserIdEditRoute
  '/_signedin/$businessId/$storeId/onboarding/': typeof SignedinBusinessIdStoreIdOnboardingIndexRoute
  '/_signedin/admin/businesses/$businessId/': typeof SignedinAdminBusinessesBusinessIdIndexRoute
  '/_signedin/$businessId/$storeId/_nav/checklists': typeof SignedinBusinessIdStoreIdNavChecklistsRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/checklists/_nav': typeof SignedinBusinessIdStoreIdNavChecklistsNavRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/devices/$clientId': typeof SignedinBusinessIdStoreIdNavDevicesClientIdRoute
  '/_signedin/$businessId/$storeId/_nav/education/$id': typeof SignedinBusinessIdStoreIdNavEducationIdRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/builder': typeof SignedinBusinessIdStoreIdNavSchedulesBuilderRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/insights': typeof SignedinBusinessIdStoreIdNavSchedulesInsightsRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/settings': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/store/settings': typeof SignedinBusinessIdStoreIdNavStoreSettingsRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/walkthrough/$step': typeof SignedinBusinessIdStoreIdNavWalkthroughStepRoute
  '/_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload': typeof SignedinBusinessIdStoreIdNavWalkthroughTeamMemberUploadRoute
  '/_signedin/$businessId/$storeId/print/$scheduleId/daily': typeof SignedinBusinessIdStoreIdPrintScheduleIdDailyRoute
  '/_signedin/$businessId/$storeId/print/$scheduleId/team': typeof SignedinBusinessIdStoreIdPrintScheduleIdTeamRoute
  '/_signedin/admin/businesses/$businessId/stores/create': typeof SignedinAdminBusinessesBusinessIdStoresCreateRoute
  '/_signedin/admin/newspaper/paper/$newspaperId/edit': typeof SignedinAdminNewspaperPaperNewspaperIdEditRoute
  '/_signedin/admin/newspaper/visibilityGroup/$visibilityGroupId/edit': typeof SignedinAdminNewspaperVisibilityGroupVisibilityGroupIdEditRoute
  '/_signedin/$businessId/$storeId/_nav/devices/': typeof SignedinBusinessIdStoreIdNavDevicesIndexRoute
  '/_signedin/$businessId/$storeId/_nav/education/': typeof SignedinBusinessIdStoreIdNavEducationIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/': typeof SignedinBusinessIdStoreIdNavSchedulesIndexRoute
  '/_signedin/$businessId/$storeId/_nav/walkthrough/': typeof SignedinBusinessIdStoreIdNavWalkthroughIndexRoute
  '/_signedin/$businessId/$storeId/schedules/$scheduleId/': typeof SignedinBusinessIdStoreIdSchedulesScheduleIdIndexRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdViewRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/_nav/active': typeof SignedinBusinessIdStoreIdNavChecklistsNavActiveRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded': typeof SignedinBusinessIdStoreIdNavChecklistsNavRecordedRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/_nav/templates': typeof SignedinBusinessIdStoreIdNavChecklistsNavTemplatesRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/_nav/upcoming': typeof SignedinBusinessIdStoreIdNavChecklistsNavUpcomingRoute
  '/_signedin/$businessId/$storeId/_nav/hr/documents/edit': typeof SignedinBusinessIdStoreIdNavHrDocumentsEditRoute
  '/_signedin/$businessId/$storeId/_nav/hr/documents/view': typeof SignedinBusinessIdStoreIdNavHrDocumentsViewRoute
  '/_signedin/$businessId/$storeId/_nav/hr/reminders/edit': typeof SignedinBusinessIdStoreIdNavHrRemindersEditRoute
  '/_signedin/$businessId/$storeId/_nav/hr/reminders/view': typeof SignedinBusinessIdStoreIdNavHrRemindersViewRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/$scheduleId/metrics': typeof SignedinBusinessIdStoreIdNavSchedulesScheduleIdMetricsRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/data/$dataFileId': typeof SignedinBusinessIdStoreIdNavSchedulesDataDataFileIdRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/data/upload': typeof SignedinBusinessIdStoreIdNavSchedulesDataUploadRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/settings/availability': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsAvailabilityRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/settings/break-rules': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsBreakRulesRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/settings/labor-laws': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsLaborLawsRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/settings/time-off': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsTimeOffRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/settings/validations': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsValidationsRoute
  '/_signedin/$businessId/$storeId/_nav/store/announcements/edit': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsEditRoute
  '/_signedin/$businessId/$storeId/_nav/store/announcements/view': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsViewRoute
  '/_signedin/$businessId/$storeId/_nav/store/links/$link': typeof SignedinBusinessIdStoreIdNavStoreLinksLinkRoute
  '/_signedin/$businessId/$storeId/_nav/store/settings/core-values': typeof SignedinBusinessIdStoreIdNavStoreSettingsCoreValuesRoute
  '/_signedin/$businessId/$storeId/_nav/store/settings/store-info': typeof SignedinBusinessIdStoreIdNavStoreSettingsStoreInfoRoute
  '/_signedin/$businessId/$storeId/_nav/store/settings/work-mode': typeof SignedinBusinessIdStoreIdNavStoreSettingsWorkModeRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdRouteWithChildren
  '/_signedin/$businessId/$storeId/print/insights/scheduling/shifts': typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingShiftsRoute
  '/_signedin/$businessId/$storeId/print/insights/scheduling/table': typeof SignedinBusinessIdStoreIdPrintInsightsSchedulingTableRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdIndexRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/_nav/': typeof SignedinBusinessIdStoreIdNavChecklistsNavIndexRoute
  '/_signedin/$businessId/$storeId/_nav/hr/documents/': typeof SignedinBusinessIdStoreIdNavHrDocumentsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/hr/insights/': typeof SignedinBusinessIdStoreIdNavHrInsightsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/hr/reminders/': typeof SignedinBusinessIdStoreIdNavHrRemindersIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/data/': typeof SignedinBusinessIdStoreIdNavSchedulesDataIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/reports/': typeof SignedinBusinessIdStoreIdNavSchedulesReportsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/settings/': typeof SignedinBusinessIdStoreIdNavSchedulesSettingsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/store/announcements/': typeof SignedinBusinessIdStoreIdNavStoreAnnouncementsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/store/events/': typeof SignedinBusinessIdStoreIdNavStoreEventsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/store/files/': typeof SignedinBusinessIdStoreIdNavStoreFilesIndexRoute
  '/_signedin/$businessId/$storeId/_nav/store/links/': typeof SignedinBusinessIdStoreIdNavStoreLinksIndexRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/': typeof SignedinBusinessIdStoreIdNavTeamDirectoryIndexRoute
  '/_signedin/$businessId/$storeId/_nav/team/reports/': typeof SignedinBusinessIdStoreIdNavTeamReportsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/timePunch/punches/': typeof SignedinBusinessIdStoreIdNavTimePunchPunchesIndexRoute
  '/_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/': typeof SignedinBusinessIdStoreIdNavTimePunchTeamMemberVarianceIndexRoute
  '/_signedin/$businessId/$storeId/_nav/timePunch/teamTotals/': typeof SignedinBusinessIdStoreIdNavTimePunchTeamTotalsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/timePunch/variance/': typeof SignedinBusinessIdStoreIdNavTimePunchVarianceIndexRoute
  '/_signedin/$businessId/$storeId/_nav/training/insights/': typeof SignedinBusinessIdStoreIdNavTrainingInsightsIndexRoute
  '/_signedin/admin/businesses/$businessId/stores/$storeId/': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdIndexRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId': typeof SignedinBusinessIdStoreIdNavChecklistsChecklistIdItemItemIdRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdUseRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/$forecastJobId': typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsForecastJobIdRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/$personAvailabilityId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityPersonAvailabilityIdRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/$shiftOfferId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersShiftOfferIdRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/$timeOffId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffTimeOffIdRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffCreateRoute
  '/_signedin/$businessId/$storeId/_nav/store/files/$fileId/view': typeof SignedinBusinessIdStoreIdNavStoreFilesFileIdViewRoute
  '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/store/settings/area/work-areas': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaWorkAreasRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/documents': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdDocumentsRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesRouteWithChildren
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/positions': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdPositionsRoute
  '/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/import': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsImportRoute
  '/_signedin/admin/businesses/$businessId/stores/$storeId/person/$personId': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdPersonPersonIdRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/data/files/': typeof SignedinBusinessIdStoreIdNavSchedulesDataFilesIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/': typeof SignedinBusinessIdStoreIdNavSchedulesDataForecastsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsShiftOffersIndexRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffIndexRoute
  '/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/': typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesIndexRoute
  '/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/': typeof SignedinAdminBusinessesBusinessIdStoresStoreIdChecklistsIndexRoute
  '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId': typeof SignedinBusinessIdStoreIdNavChecklistsTemplatesTemplateIdItemItemIdRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/$personAvailabilityId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditPersonAvailabilityIdRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/edit/$timeOffId': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsTimeOffEditTimeOffIdRoute
  '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/edit': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdEditRoute
  '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/positions': typeof SignedinBusinessIdStoreIdNavStoreSettingsAreaAreaIdPositionsRoute
  '/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/$jobId/edit': typeof SignedinBusinessIdStoreIdNavStoreSettingsJobTitlesJobIdEditRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/contact-details': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutContactDetailsRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/personal': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdAboutPersonalRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/info': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentInfoRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/permissions': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentPermissionsRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/wages': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdEmploymentWagesRoute
  '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/': typeof SignedinBusinessIdStoreIdNavSchedulesRequestsAvailabilityEditIndexRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesIndexRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionEditRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/$noteId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteNoteIdRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/edit': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteEditRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdFormalizeRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesCorrectiveActionCorrectiveActionIdViewRoute
  '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/cmFromAi/$actionableItemId': typeof SignedinBusinessIdStoreIdNavTeamDirectoryPersonIdNotesNoteCmFromAiActionableItemIdRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/forgot-password'
    | '/forgot-password-code'
    | '/verify-email'
    | '/admin'
    | '/import'
    | '/admin/dashboard'
    | '/$businessId/$storeId'
    | '/admin/businesses/create'
    | '/admin/education/$id'
    | '/admin/education/create'
    | '/admin/invitations/create'
    | '/admin/newspaper/visibility'
    | '/admin/users/create'
    | '/$businessId/$storeId/'
    | '/admin/businesses'
    | '/admin/education'
    | '/admin/features'
    | '/admin/invitations'
    | '/admin/newspaper'
    | '/admin/onboarding'
    | '/admin/people'
    | '/admin/stores'
    | '/admin/users'
    | '/import/store/$id'
    | '/$businessId/$storeId/education'
    | '/$businessId/$storeId/hr'
    | '/$businessId/$storeId/positions'
    | '/$businessId/$storeId/settings'
    | '/$businessId/$storeId/store'
    | '/$businessId/$storeId/team'
    | '/$businessId/$storeId/timePunch'
    | '/$businessId/$storeId/training'
    | '/$businessId/$storeId/walkthrough'
    | '/$businessId/$storeId/onboarding/complete'
    | '/$businessId/$storeId/onboarding/laborLaws'
    | '/$businessId/$storeId/onboarding/requiredBreaks'
    | '/$businessId/$storeId/reports/availability'
    | '/$businessId/$storeId/reports/hourly-sales'
    | '/admin/businesses/$businessId/invite'
    | '/admin/people/$personId/edit'
    | '/admin/people/$personId/streamchat'
    | '/admin/users/$userId/edit'
    | '/$businessId/$storeId/onboarding'
    | '/admin/businesses/$businessId'
    | '/$businessId/$storeId/checklists'
    | '/$businessId/$storeId/devices/$clientId'
    | '/$businessId/$storeId/education/$id'
    | '/$businessId/$storeId/schedules/builder'
    | '/$businessId/$storeId/schedules/insights'
    | '/$businessId/$storeId/schedules/settings'
    | '/$businessId/$storeId/store/settings'
    | '/$businessId/$storeId/walkthrough/$step'
    | '/$businessId/$storeId/walkthrough/team-member-upload'
    | '/$businessId/$storeId/print/$scheduleId/daily'
    | '/$businessId/$storeId/print/$scheduleId/team'
    | '/admin/businesses/$businessId/stores/create'
    | '/admin/newspaper/paper/$newspaperId/edit'
    | '/admin/newspaper/visibilityGroup/$visibilityGroupId/edit'
    | '/$businessId/$storeId/devices'
    | '/$businessId/$storeId/education/'
    | '/$businessId/$storeId/schedules'
    | '/$businessId/$storeId/walkthrough/'
    | '/$businessId/$storeId/schedules/$scheduleId'
    | '/$businessId/$storeId/checklists/$checklistId/view'
    | '/$businessId/$storeId/checklists/active'
    | '/$businessId/$storeId/checklists/recorded'
    | '/$businessId/$storeId/checklists/templates'
    | '/$businessId/$storeId/checklists/upcoming'
    | '/$businessId/$storeId/hr/documents/edit'
    | '/$businessId/$storeId/hr/documents/view'
    | '/$businessId/$storeId/hr/reminders/edit'
    | '/$businessId/$storeId/hr/reminders/view'
    | '/$businessId/$storeId/schedules/$scheduleId/metrics'
    | '/$businessId/$storeId/schedules/data/$dataFileId'
    | '/$businessId/$storeId/schedules/data/upload'
    | '/$businessId/$storeId/schedules/settings/availability'
    | '/$businessId/$storeId/schedules/settings/break-rules'
    | '/$businessId/$storeId/schedules/settings/labor-laws'
    | '/$businessId/$storeId/schedules/settings/time-off'
    | '/$businessId/$storeId/schedules/settings/validations'
    | '/$businessId/$storeId/store/announcements/edit'
    | '/$businessId/$storeId/store/announcements/view'
    | '/$businessId/$storeId/store/links/$link'
    | '/$businessId/$storeId/store/settings/core-values'
    | '/$businessId/$storeId/store/settings/store-info'
    | '/$businessId/$storeId/store/settings/work-mode'
    | '/$businessId/$storeId/team/directory/$personId'
    | '/$businessId/$storeId/print/insights/scheduling/shifts'
    | '/$businessId/$storeId/print/insights/scheduling/table'
    | '/$businessId/$storeId/checklists/$checklistId'
    | '/$businessId/$storeId/checklists/'
    | '/$businessId/$storeId/hr/documents'
    | '/$businessId/$storeId/hr/insights'
    | '/$businessId/$storeId/hr/reminders'
    | '/$businessId/$storeId/schedules/data'
    | '/$businessId/$storeId/schedules/reports'
    | '/$businessId/$storeId/schedules/requests'
    | '/$businessId/$storeId/schedules/settings/'
    | '/$businessId/$storeId/store/announcements'
    | '/$businessId/$storeId/store/events'
    | '/$businessId/$storeId/store/files'
    | '/$businessId/$storeId/store/links'
    | '/$businessId/$storeId/team/directory'
    | '/$businessId/$storeId/team/reports'
    | '/$businessId/$storeId/timePunch/punches'
    | '/$businessId/$storeId/timePunch/teamMemberVariance'
    | '/$businessId/$storeId/timePunch/teamTotals'
    | '/$businessId/$storeId/timePunch/variance'
    | '/$businessId/$storeId/training/insights'
    | '/admin/businesses/$businessId/stores/$storeId'
    | '/$businessId/$storeId/checklists/$checklistId/item/$itemId'
    | '/$businessId/$storeId/checklists/templates/$templateId/use'
    | '/$businessId/$storeId/schedules/data/forecasts/$forecastJobId'
    | '/$businessId/$storeId/schedules/requests/availability/$personAvailabilityId'
    | '/$businessId/$storeId/schedules/requests/shiftOffers/$shiftOfferId'
    | '/$businessId/$storeId/schedules/requests/timeOff/$timeOffId'
    | '/$businessId/$storeId/schedules/requests/timeOff/create'
    | '/$businessId/$storeId/store/files/$fileId/view'
    | '/$businessId/$storeId/store/settings/area/$areaId'
    | '/$businessId/$storeId/store/settings/area/work-areas'
    | '/$businessId/$storeId/team/directory/$personId/about'
    | '/$businessId/$storeId/team/directory/$personId/documents'
    | '/$businessId/$storeId/team/directory/$personId/employment'
    | '/$businessId/$storeId/team/directory/$personId/notes'
    | '/$businessId/$storeId/team/directory/$personId/positions'
    | '/admin/businesses/$businessId/stores/$storeId/checklists/import'
    | '/admin/businesses/$businessId/stores/$storeId/person/$personId'
    | '/$businessId/$storeId/checklists/templates/$templateId'
    | '/$businessId/$storeId/schedules/data/files'
    | '/$businessId/$storeId/schedules/data/forecasts'
    | '/$businessId/$storeId/schedules/requests/availability'
    | '/$businessId/$storeId/schedules/requests/shiftOffers'
    | '/$businessId/$storeId/schedules/requests/timeOff'
    | '/$businessId/$storeId/store/settings/job-titles'
    | '/admin/businesses/$businessId/stores/$storeId/checklists'
    | '/$businessId/$storeId/checklists/templates/$templateId/item/$itemId'
    | '/$businessId/$storeId/schedules/requests/availability/edit/$personAvailabilityId'
    | '/$businessId/$storeId/schedules/requests/timeOff/edit/$timeOffId'
    | '/$businessId/$storeId/store/settings/area/$areaId/edit'
    | '/$businessId/$storeId/store/settings/area/$areaId/positions'
    | '/$businessId/$storeId/store/settings/job-titles/$jobId/edit'
    | '/$businessId/$storeId/team/directory/$personId/about/contact-details'
    | '/$businessId/$storeId/team/directory/$personId/about/personal'
    | '/$businessId/$storeId/team/directory/$personId/employment/info'
    | '/$businessId/$storeId/team/directory/$personId/employment/permissions'
    | '/$businessId/$storeId/team/directory/$personId/employment/wages'
    | '/$businessId/$storeId/schedules/requests/availability/edit'
    | '/$businessId/$storeId/team/directory/$personId/notes/'
    | '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/edit'
    | '/$businessId/$storeId/team/directory/$personId/notes/note/$noteId'
    | '/$businessId/$storeId/team/directory/$personId/notes/note/edit'
    | '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize'
    | '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view'
    | '/$businessId/$storeId/team/directory/$personId/notes/note/cmFromAi/$actionableItemId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/forgot-password'
    | '/forgot-password-code'
    | '/verify-email'
    | '/admin'
    | '/import'
    | '/admin/dashboard'
    | '/$businessId/$storeId'
    | '/admin/businesses/create'
    | '/admin/education/$id'
    | '/admin/education/create'
    | '/admin/invitations/create'
    | '/admin/newspaper/visibility'
    | '/admin/users/create'
    | '/admin/businesses'
    | '/admin/education'
    | '/admin/features'
    | '/admin/invitations'
    | '/admin/newspaper'
    | '/admin/onboarding'
    | '/admin/people'
    | '/admin/stores'
    | '/admin/users'
    | '/import/store/$id'
    | '/$businessId/$storeId/hr'
    | '/$businessId/$storeId/positions'
    | '/$businessId/$storeId/settings'
    | '/$businessId/$storeId/store'
    | '/$businessId/$storeId/team'
    | '/$businessId/$storeId/timePunch'
    | '/$businessId/$storeId/training'
    | '/$businessId/$storeId/onboarding/complete'
    | '/$businessId/$storeId/onboarding/laborLaws'
    | '/$businessId/$storeId/onboarding/requiredBreaks'
    | '/$businessId/$storeId/reports/availability'
    | '/$businessId/$storeId/reports/hourly-sales'
    | '/admin/businesses/$businessId/invite'
    | '/admin/people/$personId/edit'
    | '/admin/people/$personId/streamchat'
    | '/admin/users/$userId/edit'
    | '/$businessId/$storeId/onboarding'
    | '/admin/businesses/$businessId'
    | '/$businessId/$storeId/checklists'
    | '/$businessId/$storeId/devices/$clientId'
    | '/$businessId/$storeId/education/$id'
    | '/$businessId/$storeId/schedules/builder'
    | '/$businessId/$storeId/schedules/insights'
    | '/$businessId/$storeId/store/settings'
    | '/$businessId/$storeId/walkthrough/$step'
    | '/$businessId/$storeId/walkthrough/team-member-upload'
    | '/$businessId/$storeId/print/$scheduleId/daily'
    | '/$businessId/$storeId/print/$scheduleId/team'
    | '/admin/businesses/$businessId/stores/create'
    | '/admin/newspaper/paper/$newspaperId/edit'
    | '/admin/newspaper/visibilityGroup/$visibilityGroupId/edit'
    | '/$businessId/$storeId/devices'
    | '/$businessId/$storeId/education'
    | '/$businessId/$storeId/schedules'
    | '/$businessId/$storeId/walkthrough'
    | '/$businessId/$storeId/schedules/$scheduleId'
    | '/$businessId/$storeId/checklists/$checklistId/view'
    | '/$businessId/$storeId/checklists/active'
    | '/$businessId/$storeId/checklists/recorded'
    | '/$businessId/$storeId/checklists/templates'
    | '/$businessId/$storeId/checklists/upcoming'
    | '/$businessId/$storeId/hr/documents/edit'
    | '/$businessId/$storeId/hr/documents/view'
    | '/$businessId/$storeId/hr/reminders/edit'
    | '/$businessId/$storeId/hr/reminders/view'
    | '/$businessId/$storeId/schedules/$scheduleId/metrics'
    | '/$businessId/$storeId/schedules/data/$dataFileId'
    | '/$businessId/$storeId/schedules/data/upload'
    | '/$businessId/$storeId/schedules/settings/availability'
    | '/$businessId/$storeId/schedules/settings/break-rules'
    | '/$businessId/$storeId/schedules/settings/labor-laws'
    | '/$businessId/$storeId/schedules/settings/time-off'
    | '/$businessId/$storeId/schedules/settings/validations'
    | '/$businessId/$storeId/store/announcements/edit'
    | '/$businessId/$storeId/store/announcements/view'
    | '/$businessId/$storeId/store/links/$link'
    | '/$businessId/$storeId/store/settings/core-values'
    | '/$businessId/$storeId/store/settings/store-info'
    | '/$businessId/$storeId/store/settings/work-mode'
    | '/$businessId/$storeId/team/directory/$personId'
    | '/$businessId/$storeId/print/insights/scheduling/shifts'
    | '/$businessId/$storeId/print/insights/scheduling/table'
    | '/$businessId/$storeId/checklists/$checklistId'
    | '/$businessId/$storeId/hr/documents'
    | '/$businessId/$storeId/hr/insights'
    | '/$businessId/$storeId/hr/reminders'
    | '/$businessId/$storeId/schedules/data'
    | '/$businessId/$storeId/schedules/reports'
    | '/$businessId/$storeId/schedules/requests'
    | '/$businessId/$storeId/schedules/settings'
    | '/$businessId/$storeId/store/announcements'
    | '/$businessId/$storeId/store/events'
    | '/$businessId/$storeId/store/files'
    | '/$businessId/$storeId/store/links'
    | '/$businessId/$storeId/team/directory'
    | '/$businessId/$storeId/team/reports'
    | '/$businessId/$storeId/timePunch/punches'
    | '/$businessId/$storeId/timePunch/teamMemberVariance'
    | '/$businessId/$storeId/timePunch/teamTotals'
    | '/$businessId/$storeId/timePunch/variance'
    | '/$businessId/$storeId/training/insights'
    | '/admin/businesses/$businessId/stores/$storeId'
    | '/$businessId/$storeId/checklists/$checklistId/item/$itemId'
    | '/$businessId/$storeId/checklists/templates/$templateId/use'
    | '/$businessId/$storeId/schedules/data/forecasts/$forecastJobId'
    | '/$businessId/$storeId/schedules/requests/availability/$personAvailabilityId'
    | '/$businessId/$storeId/schedules/requests/shiftOffers/$shiftOfferId'
    | '/$businessId/$storeId/schedules/requests/timeOff/$timeOffId'
    | '/$businessId/$storeId/schedules/requests/timeOff/create'
    | '/$businessId/$storeId/store/files/$fileId/view'
    | '/$businessId/$storeId/store/settings/area/$areaId'
    | '/$businessId/$storeId/store/settings/area/work-areas'
    | '/$businessId/$storeId/team/directory/$personId/about'
    | '/$businessId/$storeId/team/directory/$personId/documents'
    | '/$businessId/$storeId/team/directory/$personId/employment'
    | '/$businessId/$storeId/team/directory/$personId/positions'
    | '/admin/businesses/$businessId/stores/$storeId/checklists/import'
    | '/admin/businesses/$businessId/stores/$storeId/person/$personId'
    | '/$businessId/$storeId/checklists/templates/$templateId'
    | '/$businessId/$storeId/schedules/data/files'
    | '/$businessId/$storeId/schedules/data/forecasts'
    | '/$businessId/$storeId/schedules/requests/availability'
    | '/$businessId/$storeId/schedules/requests/shiftOffers'
    | '/$businessId/$storeId/schedules/requests/timeOff'
    | '/$businessId/$storeId/store/settings/job-titles'
    | '/admin/businesses/$businessId/stores/$storeId/checklists'
    | '/$businessId/$storeId/checklists/templates/$templateId/item/$itemId'
    | '/$businessId/$storeId/schedules/requests/availability/edit/$personAvailabilityId'
    | '/$businessId/$storeId/schedules/requests/timeOff/edit/$timeOffId'
    | '/$businessId/$storeId/store/settings/area/$areaId/edit'
    | '/$businessId/$storeId/store/settings/area/$areaId/positions'
    | '/$businessId/$storeId/store/settings/job-titles/$jobId/edit'
    | '/$businessId/$storeId/team/directory/$personId/about/contact-details'
    | '/$businessId/$storeId/team/directory/$personId/about/personal'
    | '/$businessId/$storeId/team/directory/$personId/employment/info'
    | '/$businessId/$storeId/team/directory/$personId/employment/permissions'
    | '/$businessId/$storeId/team/directory/$personId/employment/wages'
    | '/$businessId/$storeId/schedules/requests/availability/edit'
    | '/$businessId/$storeId/team/directory/$personId/notes'
    | '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/edit'
    | '/$businessId/$storeId/team/directory/$personId/notes/note/$noteId'
    | '/$businessId/$storeId/team/directory/$personId/notes/note/edit'
    | '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize'
    | '/$businessId/$storeId/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view'
    | '/$businessId/$storeId/team/directory/$personId/notes/note/cmFromAi/$actionableItemId'
  id:
    | '__root__'
    | '/'
    | '/_signedin'
    | '/forgot-password'
    | '/forgot-password-code'
    | '/verify-email'
    | '/_signedin/admin'
    | '/import/'
    | '/_signedin/admin/dashboard'
    | '/_signedin/$businessId/$storeId'
    | '/_signedin/$businessId/$storeId/_nav'
    | '/_signedin/admin/businesses/create'
    | '/_signedin/admin/education/$id'
    | '/_signedin/admin/education/create'
    | '/_signedin/admin/invitations/create'
    | '/_signedin/admin/newspaper/visibility'
    | '/_signedin/admin/users/create'
    | '/_signedin/$businessId/$storeId/'
    | '/_signedin/admin/businesses/'
    | '/_signedin/admin/education/'
    | '/_signedin/admin/features/'
    | '/_signedin/admin/invitations/'
    | '/_signedin/admin/newspaper/'
    | '/_signedin/admin/onboarding/'
    | '/_signedin/admin/people/'
    | '/_signedin/admin/stores/'
    | '/_signedin/admin/users/'
    | '/import/store/$id/'
    | '/_signedin/$businessId/$storeId/_nav/education'
    | '/_signedin/$businessId/$storeId/_nav/hr'
    | '/_signedin/$businessId/$storeId/_nav/positions'
    | '/_signedin/$businessId/$storeId/_nav/settings'
    | '/_signedin/$businessId/$storeId/_nav/store'
    | '/_signedin/$businessId/$storeId/_nav/team'
    | '/_signedin/$businessId/$storeId/_nav/timePunch'
    | '/_signedin/$businessId/$storeId/_nav/training'
    | '/_signedin/$businessId/$storeId/_nav/walkthrough'
    | '/_signedin/$businessId/$storeId/onboarding/complete'
    | '/_signedin/$businessId/$storeId/onboarding/laborLaws'
    | '/_signedin/$businessId/$storeId/onboarding/requiredBreaks'
    | '/_signedin/$businessId/$storeId/reports/availability'
    | '/_signedin/$businessId/$storeId/reports/hourly-sales'
    | '/_signedin/admin/businesses/$businessId/invite'
    | '/_signedin/admin/people/$personId/edit'
    | '/_signedin/admin/people/$personId/streamchat'
    | '/_signedin/admin/users/$userId/edit'
    | '/_signedin/$businessId/$storeId/onboarding/'
    | '/_signedin/admin/businesses/$businessId/'
    | '/_signedin/$businessId/$storeId/_nav/checklists'
    | '/_signedin/$businessId/$storeId/_nav/checklists/_nav'
    | '/_signedin/$businessId/$storeId/_nav/devices/$clientId'
    | '/_signedin/$businessId/$storeId/_nav/education/$id'
    | '/_signedin/$businessId/$storeId/_nav/schedules/builder'
    | '/_signedin/$businessId/$storeId/_nav/schedules/insights'
    | '/_signedin/$businessId/$storeId/_nav/schedules/settings'
    | '/_signedin/$businessId/$storeId/_nav/store/settings'
    | '/_signedin/$businessId/$storeId/_nav/walkthrough/$step'
    | '/_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload'
    | '/_signedin/$businessId/$storeId/print/$scheduleId/daily'
    | '/_signedin/$businessId/$storeId/print/$scheduleId/team'
    | '/_signedin/admin/businesses/$businessId/stores/create'
    | '/_signedin/admin/newspaper/paper/$newspaperId/edit'
    | '/_signedin/admin/newspaper/visibilityGroup/$visibilityGroupId/edit'
    | '/_signedin/$businessId/$storeId/_nav/devices/'
    | '/_signedin/$businessId/$storeId/_nav/education/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/'
    | '/_signedin/$businessId/$storeId/_nav/walkthrough/'
    | '/_signedin/$businessId/$storeId/schedules/$scheduleId/'
    | '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view'
    | '/_signedin/$businessId/$storeId/_nav/checklists/_nav/active'
    | '/_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded'
    | '/_signedin/$businessId/$storeId/_nav/checklists/_nav/templates'
    | '/_signedin/$businessId/$storeId/_nav/checklists/_nav/upcoming'
    | '/_signedin/$businessId/$storeId/_nav/hr/documents/edit'
    | '/_signedin/$businessId/$storeId/_nav/hr/documents/view'
    | '/_signedin/$businessId/$storeId/_nav/hr/reminders/edit'
    | '/_signedin/$businessId/$storeId/_nav/hr/reminders/view'
    | '/_signedin/$businessId/$storeId/_nav/schedules/$scheduleId/metrics'
    | '/_signedin/$businessId/$storeId/_nav/schedules/data/$dataFileId'
    | '/_signedin/$businessId/$storeId/_nav/schedules/data/upload'
    | '/_signedin/$businessId/$storeId/_nav/schedules/settings/availability'
    | '/_signedin/$businessId/$storeId/_nav/schedules/settings/break-rules'
    | '/_signedin/$businessId/$storeId/_nav/schedules/settings/labor-laws'
    | '/_signedin/$businessId/$storeId/_nav/schedules/settings/time-off'
    | '/_signedin/$businessId/$storeId/_nav/schedules/settings/validations'
    | '/_signedin/$businessId/$storeId/_nav/store/announcements/edit'
    | '/_signedin/$businessId/$storeId/_nav/store/announcements/view'
    | '/_signedin/$businessId/$storeId/_nav/store/links/$link'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/core-values'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/store-info'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/work-mode'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId'
    | '/_signedin/$businessId/$storeId/print/insights/scheduling/shifts'
    | '/_signedin/$businessId/$storeId/print/insights/scheduling/table'
    | '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/'
    | '/_signedin/$businessId/$storeId/_nav/checklists/_nav/'
    | '/_signedin/$businessId/$storeId/_nav/hr/documents/'
    | '/_signedin/$businessId/$storeId/_nav/hr/insights/'
    | '/_signedin/$businessId/$storeId/_nav/hr/reminders/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/data/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/reports/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/settings/'
    | '/_signedin/$businessId/$storeId/_nav/store/announcements/'
    | '/_signedin/$businessId/$storeId/_nav/store/events/'
    | '/_signedin/$businessId/$storeId/_nav/store/files/'
    | '/_signedin/$businessId/$storeId/_nav/store/links/'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/'
    | '/_signedin/$businessId/$storeId/_nav/team/reports/'
    | '/_signedin/$businessId/$storeId/_nav/timePunch/punches/'
    | '/_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/'
    | '/_signedin/$businessId/$storeId/_nav/timePunch/teamTotals/'
    | '/_signedin/$businessId/$storeId/_nav/timePunch/variance/'
    | '/_signedin/$businessId/$storeId/_nav/training/insights/'
    | '/_signedin/admin/businesses/$businessId/stores/$storeId/'
    | '/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId'
    | '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use'
    | '/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/$forecastJobId'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/$personAvailabilityId'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/$shiftOfferId'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/$timeOffId'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create'
    | '/_signedin/$businessId/$storeId/_nav/store/files/$fileId/view'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/area/work-areas'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/documents'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/positions'
    | '/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/import'
    | '/_signedin/admin/businesses/$businessId/stores/$storeId/person/$personId'
    | '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/data/files/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/'
    | '/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/'
    | '/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/$personAvailabilityId'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/edit/$timeOffId'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/edit'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/positions'
    | '/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/$jobId/edit'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/contact-details'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/personal'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/info'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/permissions'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/wages'
    | '/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/$noteId'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/edit'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view'
    | '/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/cmFromAi/$actionableItemId'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  SignedinRoute: typeof SignedinRouteWithChildren
  ForgotPasswordRoute: typeof ForgotPasswordRoute
  ForgotPasswordCodeRoute: typeof ForgotPasswordCodeRoute
  VerifyEmailRoute: typeof VerifyEmailRoute
  ImportIndexRoute: typeof ImportIndexRoute
  ImportStoreIdIndexRoute: typeof ImportStoreIdIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  SignedinRoute: SignedinRouteWithChildren,
  ForgotPasswordRoute: ForgotPasswordRoute,
  ForgotPasswordCodeRoute: ForgotPasswordCodeRoute,
  VerifyEmailRoute: VerifyEmailRoute,
  ImportIndexRoute: ImportIndexRoute,
  ImportStoreIdIndexRoute: ImportStoreIdIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_signedin",
        "/forgot-password",
        "/forgot-password-code",
        "/verify-email",
        "/import/",
        "/import/store/$id/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_signedin": {
      "filePath": "_signedin.tsx",
      "children": [
        "/_signedin/admin",
        "/_signedin/$businessId/$storeId"
      ]
    },
    "/forgot-password": {
      "filePath": "forgot-password.tsx"
    },
    "/forgot-password-code": {
      "filePath": "forgot-password-code.tsx"
    },
    "/verify-email": {
      "filePath": "verify-email.tsx"
    },
    "/_signedin/admin": {
      "filePath": "_signedin/admin.tsx",
      "parent": "/_signedin",
      "children": [
        "/_signedin/admin/dashboard",
        "/_signedin/admin/businesses/create",
        "/_signedin/admin/education/$id",
        "/_signedin/admin/education/create",
        "/_signedin/admin/invitations/create",
        "/_signedin/admin/newspaper/visibility",
        "/_signedin/admin/users/create",
        "/_signedin/admin/businesses/",
        "/_signedin/admin/education/",
        "/_signedin/admin/features/",
        "/_signedin/admin/invitations/",
        "/_signedin/admin/newspaper/",
        "/_signedin/admin/onboarding/",
        "/_signedin/admin/people/",
        "/_signedin/admin/stores/",
        "/_signedin/admin/users/",
        "/_signedin/admin/businesses/$businessId/invite",
        "/_signedin/admin/people/$personId/edit",
        "/_signedin/admin/people/$personId/streamchat",
        "/_signedin/admin/users/$userId/edit",
        "/_signedin/admin/businesses/$businessId/",
        "/_signedin/admin/businesses/$businessId/stores/create",
        "/_signedin/admin/newspaper/paper/$newspaperId/edit",
        "/_signedin/admin/newspaper/visibilityGroup/$visibilityGroupId/edit",
        "/_signedin/admin/businesses/$businessId/stores/$storeId/",
        "/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/import",
        "/_signedin/admin/businesses/$businessId/stores/$storeId/person/$personId",
        "/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/"
      ]
    },
    "/import/": {
      "filePath": "import/index.tsx"
    },
    "/_signedin/admin/dashboard": {
      "filePath": "_signedin/admin/dashboard.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/$businessId/$storeId": {
      "filePath": "_signedin/$businessId/$storeId",
      "parent": "/_signedin",
      "children": [
        "/_signedin/$businessId/$storeId/_nav",
        "/_signedin/$businessId/$storeId/",
        "/_signedin/$businessId/$storeId/onboarding/complete",
        "/_signedin/$businessId/$storeId/onboarding/laborLaws",
        "/_signedin/$businessId/$storeId/onboarding/requiredBreaks",
        "/_signedin/$businessId/$storeId/reports/availability",
        "/_signedin/$businessId/$storeId/reports/hourly-sales",
        "/_signedin/$businessId/$storeId/onboarding/",
        "/_signedin/$businessId/$storeId/print/$scheduleId/daily",
        "/_signedin/$businessId/$storeId/print/$scheduleId/team",
        "/_signedin/$businessId/$storeId/schedules/$scheduleId/",
        "/_signedin/$businessId/$storeId/print/insights/scheduling/shifts",
        "/_signedin/$businessId/$storeId/print/insights/scheduling/table"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav": {
      "filePath": "_signedin/$businessId/$storeId/_nav.tsx",
      "parent": "/_signedin/$businessId/$storeId",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/education",
        "/_signedin/$businessId/$storeId/_nav/hr",
        "/_signedin/$businessId/$storeId/_nav/positions",
        "/_signedin/$businessId/$storeId/_nav/settings",
        "/_signedin/$businessId/$storeId/_nav/store",
        "/_signedin/$businessId/$storeId/_nav/team",
        "/_signedin/$businessId/$storeId/_nav/timePunch",
        "/_signedin/$businessId/$storeId/_nav/training",
        "/_signedin/$businessId/$storeId/_nav/walkthrough",
        "/_signedin/$businessId/$storeId/_nav/checklists",
        "/_signedin/$businessId/$storeId/_nav/devices/$clientId",
        "/_signedin/$businessId/$storeId/_nav/schedules/builder",
        "/_signedin/$businessId/$storeId/_nav/schedules/insights",
        "/_signedin/$businessId/$storeId/_nav/schedules/settings",
        "/_signedin/$businessId/$storeId/_nav/devices/",
        "/_signedin/$businessId/$storeId/_nav/schedules/",
        "/_signedin/$businessId/$storeId/_nav/schedules/$scheduleId/metrics",
        "/_signedin/$businessId/$storeId/_nav/schedules/data/$dataFileId",
        "/_signedin/$businessId/$storeId/_nav/schedules/data/upload",
        "/_signedin/$businessId/$storeId/_nav/schedules/data/",
        "/_signedin/$businessId/$storeId/_nav/schedules/reports/",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/",
        "/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/$forecastJobId",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/$personAvailabilityId",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/$shiftOfferId",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/$timeOffId",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create",
        "/_signedin/$businessId/$storeId/_nav/schedules/data/files/",
        "/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/$personAvailabilityId",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/edit/$timeOffId",
        "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/"
      ]
    },
    "/_signedin/admin/businesses/create": {
      "filePath": "_signedin/admin/businesses/create.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/education/$id": {
      "filePath": "_signedin/admin/education/$id.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/education/create": {
      "filePath": "_signedin/admin/education/create.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/invitations/create": {
      "filePath": "_signedin/admin/invitations/create.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/newspaper/visibility": {
      "filePath": "_signedin/admin/newspaper/visibility.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/users/create": {
      "filePath": "_signedin/admin/users/create.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/$businessId/$storeId/": {
      "filePath": "_signedin/$businessId/$storeId/index.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/admin/businesses/": {
      "filePath": "_signedin/admin/businesses/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/education/": {
      "filePath": "_signedin/admin/education/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/features/": {
      "filePath": "_signedin/admin/features/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/invitations/": {
      "filePath": "_signedin/admin/invitations/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/newspaper/": {
      "filePath": "_signedin/admin/newspaper/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/onboarding/": {
      "filePath": "_signedin/admin/onboarding/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/people/": {
      "filePath": "_signedin/admin/people/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/stores/": {
      "filePath": "_signedin/admin/stores/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/users/": {
      "filePath": "_signedin/admin/users/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/import/store/$id/": {
      "filePath": "import/store/$id/index.tsx"
    },
    "/_signedin/$businessId/$storeId/_nav/education": {
      "filePath": "_signedin/$businessId/$storeId/_nav/education.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/education/$id",
        "/_signedin/$businessId/$storeId/_nav/education/"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/hr": {
      "filePath": "_signedin/$businessId/$storeId/_nav/hr.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/hr/documents/edit",
        "/_signedin/$businessId/$storeId/_nav/hr/documents/view",
        "/_signedin/$businessId/$storeId/_nav/hr/reminders/edit",
        "/_signedin/$businessId/$storeId/_nav/hr/reminders/view",
        "/_signedin/$businessId/$storeId/_nav/hr/documents/",
        "/_signedin/$businessId/$storeId/_nav/hr/insights/",
        "/_signedin/$businessId/$storeId/_nav/hr/reminders/"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/positions": {
      "filePath": "_signedin/$businessId/$storeId/_nav/positions.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/settings": {
      "filePath": "_signedin/$businessId/$storeId/_nav/settings.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/store": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/store/settings",
        "/_signedin/$businessId/$storeId/_nav/store/announcements/edit",
        "/_signedin/$businessId/$storeId/_nav/store/announcements/view",
        "/_signedin/$businessId/$storeId/_nav/store/links/$link",
        "/_signedin/$businessId/$storeId/_nav/store/announcements/",
        "/_signedin/$businessId/$storeId/_nav/store/events/",
        "/_signedin/$businessId/$storeId/_nav/store/files/",
        "/_signedin/$businessId/$storeId/_nav/store/links/",
        "/_signedin/$businessId/$storeId/_nav/store/files/$fileId/view"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/team": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId",
        "/_signedin/$businessId/$storeId/_nav/team/directory/",
        "/_signedin/$businessId/$storeId/_nav/team/reports/"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/timePunch": {
      "filePath": "_signedin/$businessId/$storeId/_nav/timePunch.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/timePunch/punches/",
        "/_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/",
        "/_signedin/$businessId/$storeId/_nav/timePunch/teamTotals/",
        "/_signedin/$businessId/$storeId/_nav/timePunch/variance/"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/training": {
      "filePath": "_signedin/$businessId/$storeId/_nav/training.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/training/insights/"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/walkthrough": {
      "filePath": "_signedin/$businessId/$storeId/_nav/walkthrough.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/walkthrough/$step",
        "/_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload",
        "/_signedin/$businessId/$storeId/_nav/walkthrough/"
      ]
    },
    "/_signedin/$businessId/$storeId/onboarding/complete": {
      "filePath": "_signedin/$businessId/$storeId/onboarding/complete.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/$businessId/$storeId/onboarding/laborLaws": {
      "filePath": "_signedin/$businessId/$storeId/onboarding/laborLaws.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/$businessId/$storeId/onboarding/requiredBreaks": {
      "filePath": "_signedin/$businessId/$storeId/onboarding/requiredBreaks.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/$businessId/$storeId/reports/availability": {
      "filePath": "_signedin/$businessId/$storeId/reports/availability.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/$businessId/$storeId/reports/hourly-sales": {
      "filePath": "_signedin/$businessId/$storeId/reports/hourly-sales.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/admin/businesses/$businessId/invite": {
      "filePath": "_signedin/admin/businesses/$businessId/invite.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/people/$personId/edit": {
      "filePath": "_signedin/admin/people/$personId.edit.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/people/$personId/streamchat": {
      "filePath": "_signedin/admin/people/$personId.streamchat.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/users/$userId/edit": {
      "filePath": "_signedin/admin/users/$userId.edit.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/$businessId/$storeId/onboarding/": {
      "filePath": "_signedin/$businessId/$storeId/onboarding/index.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/admin/businesses/$businessId/": {
      "filePath": "_signedin/admin/businesses/$businessId/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/checklists/_nav",
        "/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view",
        "/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/",
        "/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId",
        "/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use",
        "/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/",
        "/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/_nav": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/_nav.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/checklists/_nav/active",
        "/_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded",
        "/_signedin/$businessId/$storeId/_nav/checklists/_nav/templates",
        "/_signedin/$businessId/$storeId/_nav/checklists/_nav/upcoming",
        "/_signedin/$businessId/$storeId/_nav/checklists/_nav/"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/devices/$clientId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/devices/$clientId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/education/$id": {
      "filePath": "_signedin/$businessId/$storeId/_nav/education/$id.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/education"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/builder": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/builder.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/insights": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/insights.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/settings": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/settings.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/schedules/settings/availability",
        "/_signedin/$businessId/$storeId/_nav/schedules/settings/break-rules",
        "/_signedin/$businessId/$storeId/_nav/schedules/settings/labor-laws",
        "/_signedin/$businessId/$storeId/_nav/schedules/settings/time-off",
        "/_signedin/$businessId/$storeId/_nav/schedules/settings/validations",
        "/_signedin/$businessId/$storeId/_nav/schedules/settings/"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/store/settings/core-values",
        "/_signedin/$businessId/$storeId/_nav/store/settings/store-info",
        "/_signedin/$businessId/$storeId/_nav/store/settings/work-mode",
        "/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId",
        "/_signedin/$businessId/$storeId/_nav/store/settings/area/work-areas",
        "/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/",
        "/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/$jobId/edit"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/walkthrough/$step": {
      "filePath": "_signedin/$businessId/$storeId/_nav/walkthrough/$step.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/walkthrough"
    },
    "/_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload": {
      "filePath": "_signedin/$businessId/$storeId/_nav/walkthrough/team-member-upload.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/walkthrough"
    },
    "/_signedin/$businessId/$storeId/print/$scheduleId/daily": {
      "filePath": "_signedin/$businessId/$storeId/print/$scheduleId/daily.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/$businessId/$storeId/print/$scheduleId/team": {
      "filePath": "_signedin/$businessId/$storeId/print/$scheduleId/team.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/admin/businesses/$businessId/stores/create": {
      "filePath": "_signedin/admin/businesses/$businessId/stores/create.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/newspaper/paper/$newspaperId/edit": {
      "filePath": "_signedin/admin/newspaper/paper/$newspaperId/edit.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/newspaper/visibilityGroup/$visibilityGroupId/edit": {
      "filePath": "_signedin/admin/newspaper/visibilityGroup/$visibilityGroupId/edit.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/$businessId/$storeId/_nav/devices/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/devices/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/education/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/education/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/education"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/walkthrough/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/walkthrough/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/walkthrough"
    },
    "/_signedin/$businessId/$storeId/schedules/$scheduleId/": {
      "filePath": "_signedin/$businessId/$storeId/schedules/$scheduleId/index.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/$checklistId/view.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/_nav/active": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/_nav/active.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/_nav/recorded.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/_nav/templates": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/_nav/templates.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/_nav/upcoming": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/_nav/upcoming.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/hr/documents/edit": {
      "filePath": "_signedin/$businessId/$storeId/_nav/hr/documents/edit.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/hr"
    },
    "/_signedin/$businessId/$storeId/_nav/hr/documents/view": {
      "filePath": "_signedin/$businessId/$storeId/_nav/hr/documents/view.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/hr"
    },
    "/_signedin/$businessId/$storeId/_nav/hr/reminders/edit": {
      "filePath": "_signedin/$businessId/$storeId/_nav/hr/reminders/edit.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/hr"
    },
    "/_signedin/$businessId/$storeId/_nav/hr/reminders/view": {
      "filePath": "_signedin/$businessId/$storeId/_nav/hr/reminders/view.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/hr"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/$scheduleId/metrics": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/$scheduleId/metrics.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/data/$dataFileId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/data/$dataFileId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/data/upload": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/data/upload.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/settings/availability": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/settings/availability.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/schedules/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/settings/break-rules": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/settings/break-rules.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/schedules/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/settings/labor-laws": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/settings/labor-laws.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/schedules/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/settings/time-off": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/settings/time-off.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/schedules/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/settings/validations": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/settings/validations.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/schedules/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/store/announcements/edit": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/announcements/edit.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store"
    },
    "/_signedin/$businessId/$storeId/_nav/store/announcements/view": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/announcements/view.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store"
    },
    "/_signedin/$businessId/$storeId/_nav/store/links/$link": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/links/$link.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store"
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/core-values": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/core-values.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/store-info": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/store-info.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/work-mode": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/work-mode.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/documents",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/positions"
      ]
    },
    "/_signedin/$businessId/$storeId/print/insights/scheduling/shifts": {
      "filePath": "_signedin/$businessId/$storeId/print/insights/scheduling/shifts.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/$businessId/$storeId/print/insights/scheduling/table": {
      "filePath": "_signedin/$businessId/$storeId/print/insights/scheduling/table.tsx",
      "parent": "/_signedin/$businessId/$storeId"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/$checklistId/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/_nav/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/_nav/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/hr/documents/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/hr/documents/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/hr"
    },
    "/_signedin/$businessId/$storeId/_nav/hr/insights/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/hr/insights/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/hr"
    },
    "/_signedin/$businessId/$storeId/_nav/hr/reminders/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/hr/reminders/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/hr"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/data/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/data/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/reports/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/reports/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/settings/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/settings/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/schedules/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/store/announcements/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/announcements/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store"
    },
    "/_signedin/$businessId/$storeId/_nav/store/events/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/events/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store"
    },
    "/_signedin/$businessId/$storeId/_nav/store/files/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/files/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store"
    },
    "/_signedin/$businessId/$storeId/_nav/store/links/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/links/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team"
    },
    "/_signedin/$businessId/$storeId/_nav/team/reports/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/reports/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team"
    },
    "/_signedin/$businessId/$storeId/_nav/timePunch/punches/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/timePunch/punches/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/timePunch"
    },
    "/_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/timePunch/teamMemberVariance/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/timePunch"
    },
    "/_signedin/$businessId/$storeId/_nav/timePunch/teamTotals/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/timePunch/teamTotals/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/timePunch"
    },
    "/_signedin/$businessId/$storeId/_nav/timePunch/variance/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/timePunch/variance/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/timePunch"
    },
    "/_signedin/$businessId/$storeId/_nav/training/insights/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/training/insights/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/training"
    },
    "/_signedin/admin/businesses/$businessId/stores/$storeId/": {
      "filePath": "_signedin/admin/businesses/$businessId/stores/$storeId/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/$checklistId/item/$itemId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/use.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/$forecastJobId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/$forecastJobId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/$personAvailabilityId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/availability/$personAvailabilityId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/$shiftOfferId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/$shiftOfferId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/$timeOffId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/$timeOffId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/create.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/store/files/$fileId/view": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/files/$fileId/view.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store"
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/edit",
        "/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/positions"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/area/work-areas": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/area/work-areas.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/about.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/contact-details",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/personal"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/documents": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/documents.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/info",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/permissions",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/wages"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId",
      "children": [
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/$noteId",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/edit",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view",
        "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/cmFromAi/$actionableItemId"
      ]
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/positions": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/positions.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId"
    },
    "/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/import": {
      "filePath": "_signedin/admin/businesses/$businessId/stores/$storeId/checklists/import.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/admin/businesses/$businessId/stores/$storeId/person/$personId": {
      "filePath": "_signedin/admin/businesses/$businessId/stores/$storeId/person/$personId.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/data/files/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/data/files/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/data/forecasts/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/availability/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/shiftOffers/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/job-titles/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings"
    },
    "/_signedin/admin/businesses/$businessId/stores/$storeId/checklists/": {
      "filePath": "_signedin/admin/businesses/$businessId/stores/$storeId/checklists/index.tsx",
      "parent": "/_signedin/admin"
    },
    "/_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/checklists/templates/$templateId/item/$itemId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/checklists"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/$personAvailabilityId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/$personAvailabilityId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/edit/$timeOffId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/timeOff/edit/$timeOffId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/edit": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/edit.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId"
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/positions": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId/positions.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings/area/$areaId"
    },
    "/_signedin/$businessId/$storeId/_nav/store/settings/job-titles/$jobId/edit": {
      "filePath": "_signedin/$businessId/$storeId/_nav/store/settings/job-titles/$jobId/edit.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/store/settings"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/contact-details": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/contact-details.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/personal": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/about/personal.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/about"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/info": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/info.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/permissions": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/permissions.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/wages": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment/wages.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/employment"
    },
    "/_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/schedules/requests/availability/edit/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/index.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/edit.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/$noteId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/$noteId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/edit": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/edit.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/formalize.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/correctiveAction/$correctiveActionId/view.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes"
    },
    "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/cmFromAi/$actionableItemId": {
      "filePath": "_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes/note/cmFromAi/$actionableItemId.tsx",
      "parent": "/_signedin/$businessId/$storeId/_nav/team/directory/$personId/notes"
    }
  }
}
ROUTE_MANIFEST_END */
