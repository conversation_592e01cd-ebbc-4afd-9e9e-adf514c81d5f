import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title} from "@/src/components/ui/dialog";
import {Text} from "@/src/components/Text";
import {Button} from "@/src/components/ui/button";
import {api} from "@/src/api";
import {Label} from "@/src/components/ui/label.tsx";
import {FormInput} from "@/src/components/form/FormInput.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {useForm} from "@tanstack/react-form";
import {find} from "lodash";
import {toast} from "sonner";
import {z} from "zod";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {ErrorAlert} from "@/src/components/ErrorAlert.tsx";
import {DateTime} from "luxon";
import {FormSelect} from "@/src/components/form/FormSelect.tsx";
import {FormDateTimePicker} from "@/src/components/form/FormDateTimePicker.tsx";
import {EventDetailsPanelContent} from "@/src/components/EventDetailsPanelContent.tsx";
import {ScheduleEventDto} from "../../../api/src/scheduleEventSchemas.ts";

export function EditEventModal({
                                 storeId,
                                 eventId,
                                 isOpen,
                                 onOpenChange,
                               }: {
  storeId: string;
  eventId: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}) {
  const apiUtil = api.useUtils();

  const [store] = api.user.getStoreAdmin.useSuspenseQuery({storeId: storeId!}, {staleTime: 1000 * 60 * 60});
  const timezone = store.timezone;
  const now = DateTime.now().setZone(timezone);

  const [events] = api.user.getScheduleEvents.useSuspenseQuery({
    week: {year: now.weekYear, week: now.weekNumber},
    storeId: storeId
  });

  const event = find(events, (a) => a.id === eventId);
  const isNewEvent = !event;

  const defaultNewEvent: ScheduleEventDto = {
    id: eventId,
    title: "",
    description: "",
    range: {
      start: now.toJSDate(),
      end: now.toJSDate(),
    },
    eventType: "other", // or a valid default
    visibilityLevel: 0,
    isTimeOffRestricted: false,
  };

  const upsertEvent = api.user.upsertScheduleEvent.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getStoreAdmin.invalidate();
      await apiUtil.user.getScheduleEvents.invalidate({
        storeId: storeId!
      });
      toast.success(isNewEvent ? "Event created successfully" : "Event updated successfully");
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`Failed to ${isNewEvent ? "create" : "update"} event: ${error.message}`);
    },
  });

  const deleteEvent = api.user.deleteScheduleEvent.useMutation({
    onSuccess: async () => {
      await apiUtil.user.getStoreAdmin.invalidate();
      await apiUtil.user.getScheduleEvents.invalidate({
        storeId: storeId!
      });
      toast.success("Event deleted successfully");
      onOpenChange(false);
    },
    onError: (error) => {
      toast.error(`Failed to delete event: ${error.message}`);
    },
  });

  const handleUpsertEvent = (value: ScheduleEventDto) => {
    upsertEvent.mutate({
      id: value.id,
      storeId: storeId,
      title: value.title,
      description: value.description,
      range: value.range,
      eventType: value.eventType,
      visibilityLevel: value.visibilityLevel,
      isTimeOffRestricted: value.isTimeOffRestricted ?? false,
    })
  }

  const handleDeleteEvent = () => {
    if (confirm("Are you sure you want to delete this event?")) {
      deleteEvent.mutate({id: eventId});
    }
  }

  return (
          <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  <Text size="lg" semibold>
                    {isNewEvent ? "Create Event" : "Edit Event"}
                  </Text>
                </DialogTitle>
              </DialogHeader>

              {upsertEvent.isError ? <ErrorAlert error={upsertEvent.error}/> : null}
              {deleteEvent.isError ? <ErrorAlert error={deleteEvent.error}/> : null}

              <div className="space-y-4 ">
                <EventDetailsPanelContent
                        isNewEvent={isNewEvent}
                        event={event ?? defaultNewEvent}
                        onUpdateEvent={handleUpsertEvent}
                        onDeleteEvent={handleDeleteEvent}
                        isLoading={upsertEvent.isPending ?? deleteEvent.isPending}
                        timezone={timezone}
                        storeId={storeId}
                />
              </div>
              <DialogFooter></DialogFooter>
            </DialogContent>
          </Dialog>
  )
          ;
}
