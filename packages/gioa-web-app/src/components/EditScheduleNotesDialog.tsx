import React, {useEffect} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/src/components/ui/dialog.tsx";
import {zodValidator} from "@tanstack/zod-form-adapter";
import {useForm} from "@tanstack/react-form";
import {FormControl} from "@/src/components/form/FormControl.tsx";
import {FieldInfo} from "@/src/components/form/FieldInfo.tsx";
import {DayOfWeek} from "../../../api/src/timeSchemas.ts";
import {z} from "zod";
import {filter, find, isEmpty, map} from "lodash";
import {daysOfWeek} from "@/src/components/AvailabilityWeek.tsx";
import {api} from "@/src/api.ts";
import {FormTextarea} from "@/src/components/form/FormTextarea.tsx";
import {PersonAvatar} from "@/src/components/PersonAvatar.tsx";
import {formatDistanceToNow} from "date-fns";
import {genEntityNoteId} from '@gioa/api/src/schemas.ts';
import {Spinner} from "@/src/components/Spinner.tsx";
import {ArrowUpIcon} from "lucide-react";

export interface EditScheduleNotesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  dayOfWeek: DayOfWeek;
  scheduleId: string;
  week: number;
}

export const EditScheduleNotesDialog: React.FC<EditScheduleNotesDialogProps> = (props) => {
  const createNote = api.user.createScheduleNote.useMutation();
  const getNotes = api.user.getScheduleNotes.useQuery({scheduleId: props.scheduleId}, {
    select: notes => {
      return filter(notes, n => n.dayOfWeek === props.dayOfWeek);
    }
  });

  const form = useForm({
    defaultValues: {
      note: ""
    },
    onSubmit: async ({value}) => {
      createNote.mutate({
        noteId: genEntityNoteId(),
        scheduleId: props.scheduleId,
        dayOfWeek: props.dayOfWeek,
        note: value.note
      }, {
        onSuccess: () => {
          form.reset();
          getNotes.refetch();
        }
      })
    },
    validatorAdapter: zodValidator(),
  });

  useEffect(() => {
    if (!props.isOpen) {
      // give a bit of time for the modal to animate out
      setTimeout(() =>
        form.reset(), 100);
    }
  }, [props.isOpen]);

  const onCancel = () => {
    props.onClose();
  }

  const dayOfWeekObj = find(daysOfWeek, d => d.dayOfWeek === props.dayOfWeek);

  return (
    <Dialog open={props.isOpen} onOpenChange={open => {
      if (open) {
        props.onOpen();
      } else {
        onCancel();
      }
    }}>
      <DialogContent padding={"none"} className={"gap-0"}>
        <DialogHeader className={"pl-6 pr-6 pt-6 pb-3 border-b"}>
          <DialogTitle>
            Notes for {dayOfWeekObj?.name} of week {props.week}
          </DialogTitle>
        </DialogHeader>

        {getNotes.isLoading ? <Spinner size={"lg"} className={"my-6"}/> : null}

        <div>
          <div className="space-y-4 overflow-auto py-4 px-4"
               style={{maxHeight: "calc(100vh - 200px)", minHeight: "120px"}}>

            {getNotes.isSuccess && isEmpty(getNotes.data) ?
              <div className={"text-muted-foreground text-center"}>
                No notes are recorded for this day yet.
              </div> : null}

            {map(getNotes.data, note => {
              return <div key={note.id}>
                <div className={"flex gap-2 items-center mb-2"}>
                  <PersonAvatar person={note.createdByPerson}/>
                  <div className={"font-semibold"}>
                    {note.createdByPerson ? `${note.createdByPerson.firstName} ${note.createdByPerson.lastName}` : "System"}
                    <span className={"pl-2 text-muted-foreground text-sm font-normal"}>
                        {formatDistanceToNow(note.createdAt)}
                      </span>
                  </div>
                </div>
                <div className={"flex"}>
                  <div className={"w-[40px] min-w-[40px] flex justify-center"}>
                    <div className={"w-px bg-gray-300"}/>
                  </div>
                  <p className={"whitespace-pre-wrap pl-2"}>
                    {note.note}
                  </p>
                </div>
              </div>
            })}
          </div>

          <form onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}>
            <div className={"border-t pt-4 px-4 pb-4"}>
              <form.Field name={"note"}
                          validators={{
                            onSubmit: z.string().optional()
                          }}
                          children={(field) => {
                            return <FormControl className={"m-0"}>
                              <div className={"flex items-start gap-2"}>
                                <FormTextarea field={field} aria-label={"Note"}
                                              className={"min-h-[auto]"}
                                              placeholder="Enter notes..."/>
                                <button type={"submit"}
                                        className={"text-white rounded-full min-w-[40px] w-[40px] min-h-[40px] h-[40px] hover:bg-primary-500 bg-primary-600 flex items-center justify-center"}
                                        title={"Save note"}>
                                  <ArrowUpIcon size={24}/>
                                </button>
                              </div>
                              <FieldInfo field={field}/>
                            </FormControl>;
                          }}/>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
