import {employedProcedure, t, verifiedProcedure} from "../../trpc";
import {z} from "zod";
import {DateTime} from "luxon";
import {toSecondsSinceEpoch} from "../../date.util";
import {personDto, personId} from "../../schemas";
import {toSecureStoreIdOrThrow} from "../../authorization.util";
import {TRPCError} from "@trpc/server";
import {difference, filter, includes, intersection, intersectionWith, isEmpty, map, uniq} from "lodash";
import * as perm from "../../permissionChecks";
import {toPersonDto} from "../../schema.converters";
import {getOperatorPeople} from "../../personDb";
import {
  constructJobMembershipRule,
  constructPrivateMembershipRule,
  constructPublicMembershipRule
} from "../../chat/membershipRule";
import {
  createStreamChatChannel,
  createStreamChatDistinctChannel,
  getChannelTeam,
  getIsBusinessWideChannel,
  isAtLimitOfChannelCount,
  isJobChannel,
  isPrivateChannel,
  maxBusinessChannels,
  maxStoreChannels
} from "../../chat/channel";
import {buildBusinessState, syncChannelMembership, syncJobChannelMembership} from "../../chat/syncChannelMembership";
import {businessDb} from "../../businessDb";
import {evaluatePrivateChannelMembership, PrivateChannel} from "../../chat/evaluatePrivateChannelMembership";
import {SecureStoreId} from "../../database.types";

export const chatRouter = t.router({
  getApiKey: verifiedProcedure
    .output(z.object({
      apiKey: z.string(), // the NON-SENSITIVE API key for the client
    }))
    .query(async ({ctx, input}) => {
      return {
        apiKey: ctx.streamChatApiKey
      }
    }),

  provideToken: verifiedProcedure
    .output(z.object({
      token: z.string(),
    }))
    .mutation(async ({ctx, input}) => {
      const personId = ctx.auth.user.person.id!;
      const issuedAt = DateTime.now();
      const iat = toSecondsSinceEpoch(issuedAt);

      // why 8 hours? It seems short enough that if the user logs out, it leaves a short window of exploit after that logout. If the token e.g. lasted a week, that would be a longer window. We're not revoking user chat tokens on logout, so this short window is a way to reduce risk of a logged-out user's token somehow getting abused.
      const expireTime = toSecondsSinceEpoch(issuedAt.plus({hours: 8}));
      const token = await ctx.streamChatClient.createToken(personId, expireTime, iat);

      return {
        token,
      }
    }),

  /**
   * Only chat admins can create private channels.
   */
  createPrivateChannel: employedProcedure
    .input(z.object({
      storeId: z.string().optional(), // Specified if this channel is for a single store. Not specified if this channel is a business-wide channel.
      name: z.string(),
      imageId: z.string().optional(),
      members: z.array(personId).optional(),
      jobIds: z.array(z.string()).optional(),
      storePositionIds: z.array(z.string()).optional(),
      extraPeopleIds: z.array(z.string()).optional(),
    }))
    .output(z.object({
      id: z.string(),
      cid: z.string(),
      channelType: z.literal("messaging"),
    }))
    .mutation(async ({ctx, input}) => {
      const {checkAllowed} = ctx;
      if (!perm.canCreateChatChannel(checkAllowed, {
        businessId: ctx.businessId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create chat groups"
        });
      }

      if (input.storeId) {
        const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
        if (await isAtLimitOfChannelCount(ctx, storeId, maxStoreChannels)) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You have reached the maximum number of chat groups for this store: " + maxStoreChannels
          });
        }
      } else { // if no storeId, then this is a business-wide channel. The user must be employed at all the stores in the business
        const storeIds = await ctx.db.business.getStoreIds(ctx.businessId);
        const userStoreIds = ctx.storeIds;
        if (!isEmpty(difference(storeIds, userStoreIds))) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You are not employed at all stores in the business, so you cannot create a business-wide channel"
          });
        }

        if (await isAtLimitOfChannelCount(ctx, ctx.businessId, maxBusinessChannels)) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You have reached the maximum number of chat groups for this business: " + maxBusinessChannels
          });
        }
      }

      const channelType = "messaging";

      // If members are given, then this is an invite-only channel that does not use rule-based membership.
      // TODO This is DEPRECATED. Henceforth after app update, all private channels will be rule-based, where one of the rules is extraPeopleIds, which takes the place of the members list.
      if (input.members) {
        // If storeId is given, then this is a store-specific channel. The requesting user can only select people in this store.
        // If storeId is not given, then this is a business-wide channel. The requesting user can select any people in the business.
        const peopleIds = input.storeId
          ? await ctx.db.store.getActivePeopleIds(ctx.businessId, toSecureStoreIdOrThrow(ctx, input.storeId))
          : await ctx.db.business.getActivePeopleIds(ctx.businessId);
        const members = intersection(peopleIds, input.members);

        const channel = await createStreamChatChannel(ctx, {
          team: input.storeId ? toSecureStoreIdOrThrow(ctx, input.storeId) : ctx.businessId,
          channelType: channelType,
          members: members,
          channelName: input.name,
          nationMembershipRule: constructPrivateMembershipRule({}),
          isBusinessWide: !Boolean(input.storeId),
        })

        return {
          channelType,
          id: channel.id!,
          cid: channel.cid
        }
      } else { // else no members, so this is a rule-based channel. We need to evaluate the rules to determine the initial members.
        const storeIds = await ctx.db.business.getStoreIds(ctx.businessId);
        const businessState = await buildBusinessState(ctx, {
          storeIds: storeIds,
          storePositionIds: input.storePositionIds ?? []
        });
        const ruleProps = {
          jobIds: input.jobIds,
          storePositionIds: input.storePositionIds,
          extraPeopleIds: [...input.extraPeopleIds ?? [], ctx.currentPersonId],
        }

        const privateChannel: PrivateChannel = input.storeId ? {
          storeId: toSecureStoreIdOrThrow(ctx, input.storeId),
          ...ruleProps
        } : {
          businessId: ctx.businessId,
          ...ruleProps
        }

        const members = evaluatePrivateChannelMembership(privateChannel, businessState);
        const channel = await createStreamChatChannel(ctx, {
          team: input.storeId ? toSecureStoreIdOrThrow(ctx, input.storeId) : ctx.businessId,
          channelType: channelType,
          members: members,
          channelName: input.name,
          nationMembershipRule: constructPrivateMembershipRule({
            jobIds: input.jobIds,
            storePositionIds: input.storePositionIds,
            extraPeopleIds: input.extraPeopleIds,
          }),
          isBusinessWide: !Boolean(input.storeId),
        })

        return {
          channelType,
          id: channel.id!,
          cid: channel.cid
        }
      }
    }),

  /**
   * Only chat admins can create public channels.
   */
  createPublicChannel: employedProcedure
    .input(z.object({
      storeId: z.string().optional(), // Specified if this channel is for a single store. Not specified if this channel is a business-wide channel.
      name: z.string(),
      imageId: z.string().optional(),
    }))
    .output(z.object({
      id: z.string(),
      cid: z.string(),
      channelType: z.literal("messaging"),
    }))
    .mutation(async ({ctx, input}) => {
      const {checkAllowed} = ctx;
      if (!perm.canCreateChatChannel(checkAllowed, {
        businessId: ctx.businessId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create chat groups"
        });
      }

      if (input.storeId) {
        const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
        if (await isAtLimitOfChannelCount(ctx, storeId, maxStoreChannels)) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You have reached the maximum number of chat groups for this store: " + maxStoreChannels
          });
        }
      } else { // if no storeId, then this is a business-wide channel. The user must be employed at all the stores in the business
        const storeIds = await ctx.db.business.getStoreIds(ctx.businessId);
        const userStoreIds = ctx.storeIds;
        if (!isEmpty(difference(storeIds, userStoreIds))) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You are not employed at all stores in the business, so you cannot create a business-wide channel"
          });
        }

        if (await isAtLimitOfChannelCount(ctx, ctx.businessId, maxBusinessChannels)) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You have reached the maximum number of chat groups for this business: " + maxBusinessChannels
          });
        }
      }

      // If storeId is given, then this is a store-specific channel.
      if (input.storeId) {
        const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
        const storePeopleIds = await ctx.db.store.getActivePeopleIds(ctx.businessId, storeId);
        const channelType = "messaging";
        const channel = await createStreamChatChannel(ctx, {
          team: storeId,
          channelType: channelType,
          members: storePeopleIds,
          channelName: input.name,
          nationMembershipRule: constructPublicMembershipRule(),
          isBusinessWide: false,
        })

        return {
          channelType,
          id: channel.id!,
          cid: channel.cid
        }
      } else { // else no storeId, then this is a business-wide channel.
        const peopleIds = await ctx.db.business.getActivePeopleIds(ctx.businessId);
        const channelType = "messaging";
        const channel = await createStreamChatChannel(ctx, {
          team: ctx.businessId,
          channelType: channelType,
          members: peopleIds,
          channelName: input.name,
          nationMembershipRule: constructPublicMembershipRule(),
          isBusinessWide: true,
        })

        return {
          channelType,
          id: channel.id!,
          cid: channel.cid
        }
      }
    }),

  // TODO DEPRECATED. Remove in future version. Job channels are now private channels with jobIds.
  createJobsChannel: employedProcedure
    .input(z.object({
      storeId: z.string(),
      name: z.string(),
      imageId: z.string().optional(),
      jobIds: z.array(z.string()),
    }))
    .output(z.object({
      id: z.string(),
      cid: z.string(),
      channelType: z.literal("messaging"),
    }))
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {checkAllowed} = ctx;
      if (!perm.canCreateChatChannel(checkAllowed, {
        businessId: ctx.businessId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create chat groups"
        });
      }

      if (await isAtLimitOfChannelCount(ctx, storeId, maxStoreChannels)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You have reached the maximum number of chat groups for this store: " + maxStoreChannels
        });
      }

      const members = await ctx.db.store.getActivePeopleIdsWhoHaveJobs(ctx.businessId, storeId, input.jobIds);
      const channelType = "messaging";
      const channel = await createStreamChatChannel(ctx, {
        team: storeId,
        channelType: channelType,
        members: [...members, ctx.currentPersonId],
        channelName: input.name,
        nationMembershipRule: constructJobMembershipRule(input.jobIds),
        isBusinessWide: false
      })

      return {
        channelType,
        id: channel.id!,
        cid: channel.cid
      }
    }),

  /**
   * Edit a private channel's membership rules. The user can NOT edit the channel's store or business-wide-ness.
   */
  editPrivateChannel: employedProcedure
    .input(z.object({
      cid: z.string(),
      jobIds: z.array(z.string()).optional(),
      storePositionIds: z.array(z.string()).optional(),
      extraPeopleIds: z.array(z.string()).optional(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {checkAllowed} = ctx;
      if (!perm.canCreateChatChannel(checkAllowed, {
        businessId: ctx.businessId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit chat groups"
        });
      }

      const channels = await ctx.streamChatClient.queryChannels({cid: input.cid});
      if (isEmpty(channels)) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Group not found"
        });
      }

      const channel = channels[0];

      if (!isPrivateChannel(channel)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "This is not a private group"
        });
      }

      const isBusinessChannel = getIsBusinessWideChannel(channel);
      if (isBusinessChannel) { // if this is a business-wide channel. The user must be employed at all the stores in the business
        const storeIds = await ctx.db.business.getStoreIds(ctx.businessId);
        const userStoreIds = ctx.storeIds;
        if (!isEmpty(difference(storeIds, userStoreIds))) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You are not employed at all stores in the business, so you cannot edit a business-wide channel"
          });
        }
      }

      await channel.updatePartial({
        set: {
          jobIds: input.jobIds,
          storePositionIds: input.storePositionIds,
          extraPeopleIds: input.extraPeopleIds,
        }
      });

      const storeIds = await ctx.db.business.getStoreIds(ctx.businessId);
      const businessState = await buildBusinessState(ctx, {
        storeIds: storeIds,
        storePositionIds: input.storePositionIds ?? []
      });
      const ruleProps = {
        jobIds: input.jobIds,
        storePositionIds: input.storePositionIds,
        extraPeopleIds: input.extraPeopleIds,
      }

      const privateChannel: PrivateChannel = isBusinessChannel ? {
        businessId: ctx.businessId,
        ...ruleProps
      } : {
        storeId: getChannelTeam(channel) as SecureStoreId,
        ...ruleProps
      }

      const members = evaluatePrivateChannelMembership(privateChannel, businessState);
      await syncChannelMembership(channel, members);
    }),

  editJobsChannelJobs: employedProcedure
    .input(z.object({
      storeId: z.string(),
      cid: z.string(),
      jobIds: z.array(z.string()),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
      const {checkAllowed} = ctx;
      if (!perm.canCreateChatChannel(checkAllowed, {
        businessId: ctx.businessId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to edit chat groups"
        });
      }

      const channels = await ctx.streamChatClient.queryChannels({cid: input.cid});
      if (isEmpty(channels)) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Group not found"
        });
      }

      const channel = channels[0];

      // Verify this is a job channel. Note that standalone job channels are deprecated. They are now all private channels.
      if (!isJobChannel(channel)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "This is not a job channel"
        });
      }

      // Update the channel's job IDs
      await channel.updatePartial({
        set: {
          nationMembershipRuleType: "job",
          jobIds: input.jobIds
        }
      });

      await syncJobChannelMembership(ctx, {channel, storeId, jobIds: input.jobIds});
    }),

  /**
   * Anyone approved TM in the store can create a direct channel with anyone else in the store.
   */
  createDirectChannel: employedProcedure
    .input(z.object({
      //TODO DEPRECATED... Direct channels are now business-wide. Remove storeId in future version.
      storeId: z.string().optional(),
      members: z.array(personId),
    }))
    .output(z.object({
      id: z.string(),
      cid: z.string(),
      channelType: z.literal("direct"),
    }))
    .mutation(async ({ctx, input}) => {
      // if a storeId is passed in, then this is an old app version, so do what you used to do for backwards compatibility. TODO This is legacy code. Remove after app update.
      if (input.storeId) {
        const storeId = toSecureStoreIdOrThrow(ctx, input.storeId);
        const channelType = "direct";
        const storePeopleIds = await ctx.db.store.getActivePeopleIds(ctx.businessId, storeId);
        const members = uniq(intersection(storePeopleIds, [...input.members, ctx.currentPersonId]));

        // Note we don't check for max number of direct groups, since there is a natural limit already (each combination of people in the store)

        // Create a distinct channel (no ID, just members)
        const channel = await createStreamChatDistinctChannel(ctx, {
          team: storeId,
          members: members,
          channelType: channelType
        })

        return {
          channelType,
          id: channel.id!,
          cid: channel.cid
        }
      }

      const channelType = "direct";
      const businessPeopleIds = await ctx.db.business.getActivePeopleIdsAtStores(ctx.businessId, ctx.storeIds);
      const members = uniq(intersection(businessPeopleIds, [...input.members, ctx.currentPersonId]));

      // Note we don't check for max number of direct groups, since there is a natural limit already (each combination of people in the store)

      // Create a distinct channel (no ID, just members)
      const channel = await createStreamChatDistinctChannel(ctx, {
        team: ctx.businessId,
        members: members,
        channelType: channelType
      })

      return {
        channelType,
        id: channel.id!,
        cid: channel.cid
      }
    }),

  disableChannel: employedProcedure
    .input(z.object({
      channelId: z.string(),
      channelType: z.enum(["messaging", "team"]),
      storeId: z.string().optional() // TODO DEPRECATED. Remove in future version.
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const {checkAllowed} = ctx;
      if (!perm.canArchiveChatChannel(checkAllowed, {
        businessId: ctx.businessId,
      })) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to archive chat groups"
        });
      }

      const filter = {type: input.channelType, id: input.channelId, team: {$in: [ctx.businessId, ...ctx.storeIds]}};
      const channels = await ctx.streamChatClient.queryChannels(filter);
      if (isEmpty(channels)) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Group not found"
        });
      }

      const channel = channels[0];
      await channel.updatePartial({
        set: {disabled: true}
      });
    }),

  /**
   * ###################################################### WARNING: OPERATOR ONLY! ######################################################
   * Get all operators in Nation that are available to be messaged. An operator is identified by their Job createdFromTemplateId being "operator".
   */
  getAvailableOperators: employedProcedure
    .output(z.object({
      people: z.array(personDto)
    }))
    .query(async ({ctx}) => {
      if (!perm.isAnOperator(ctx.employment)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create operator groups"
        });
      }

      const operatorPeople = await getOperatorPeople(ctx.prisma);

      return {
        people: map(operatorPeople, p => toPersonDto(ctx, p))
      }
    }),

  /**
   * ###################################################### WARNING: OPERATOR ONLY! ######################################################
   * Create an operator public channel. The channel type is "operator" and the team is "operators".
   * Public channels are created with all operators as members in the StreamChat channel membership list.
   */
  createPublicOperatorChannel: employedProcedure
    .input(z.object({
      name: z.string(),
      imageId: z.string().optional(),
    }))
    .output(z.object({
      id: z.string(),
      cid: z.string(),
      channelType: z.literal("operator"),
    }))
    .mutation(async ({ctx, input}) => {
      if (!perm.isAnOperator(ctx.employment)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create operator groups"
        });
      }

      // if we're creating a public group, then we need to add all operators as members
      const operatorPeople = await getOperatorPeople(ctx.prisma);
      const members = map(operatorPeople, p => p.id);

      const channelType = "operator";
      const channel = await createStreamChatChannel(ctx, {
        team: "operators",
        channelType: channelType,
        members: members,
        channelName: input.name,
        nationMembershipRule: constructPublicMembershipRule(),
        isBusinessWide: false
      })

      return {
        channelType,
        id: channel.id!,
        cid: channel.cid
      }
    }),

  /**
   * ###################################################### WARNING: OPERATOR ONLY! ######################################################
   * Create an operator channel. The channel type is "operator" and the team is "operators".
   * Private channels are created with just the specified members in the StreamChat channel membership list.
   */
  createPrivateOperatorChannel: employedProcedure
    .input(z.object({
      name: z.string(),
      imageId: z.string().optional(),
      members: z.array(personId),
    }))
    .output(z.object({
      id: z.string(),
      cid: z.string(),
      channelType: z.literal("operator"),
    }))
    .mutation(async ({ctx, input}) => {
      if (!perm.isAnOperator(ctx.employment)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create operator groups"
        });
      }

      // Don't allow passing in arbitrary personIds. Make sure they are indeed operators.
      const operatorPeople = await getOperatorPeople(ctx.prisma);
      const members = map(intersectionWith(operatorPeople, uniq([...input.members, ctx.currentPersonId]), (p, id) => p.id === id), p => p.id);

      const channelType = "operator";
      const channel = await createStreamChatChannel(ctx, {
        team: "operators",
        channelType: channelType,
        members: members,
        channelName: input.name,
        nationMembershipRule: constructPrivateMembershipRule({}),
        isBusinessWide: false
      })

      return {
        channelType,
        id: channel.id!,
        cid: channel.cid
      }
    }),

  /**
   * ###################################################### WARNING: OPERATOR ONLY! ######################################################
   * Create an operator channel. The channel type is "operator" and the team is "operators".
   * Direct channels are created with just the specified members in the StreamChat channel membership list and no channel ID.
   */
  createDirectOperatorChannel: employedProcedure
    .input(z.object({
      members: z.array(personId),
    }))
    .output(z.object({
      id: z.string(),
      cid: z.string(),
      channelType: z.literal("operator"),
    }))
    .mutation(async ({ctx, input}) => {
      if (!perm.isAnOperator(ctx.employment)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to create operator groups"
        });
      }

      // Don't allow passing in arbitrary personIds. Make sure they are indeed operators.
      const operatorPeople = await getOperatorPeople(ctx.prisma);
      const members = map(intersectionWith(operatorPeople, uniq([...input.members, ctx.currentPersonId]), (p, id) => p.id === id), p => p.id);

      // Create a distinct channel (no ID, just members)
      const channelType = "operator";
      const channel = await createStreamChatDistinctChannel(ctx, {
        team: "operators",
        members: members,
        channelType: channelType
      })

      return {
        channelType,
        id: channel.id!,
        cid: channel.cid
      }
    }),

  /**
   * ###################################################### WARNING: OPERATOR ONLY! ######################################################
   * Disable an operator channel. This is used to archive a channel. Only the operator that created the channel can disable it.
   */
  disableOperatorChannel: employedProcedure
    .input(z.object({
      channelId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      if (!perm.isAnOperator(ctx.employment)) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to modify operator groups"
        });
      }

      const filter = {type: "operator", id: input.channelId, team: "operators" as const};
      const channels = await ctx.streamChatClient.queryChannels(filter);
      if (isEmpty(channels)) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Group not found"
        });
      }

      const channel = channels[0];
      // check ownership of this channel
      if (channel.data?.created_by?.id !== ctx.currentPersonId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to modify this group"
        });
      }

      await channel.updatePartial({
        set: {disabled: true}
      });
    }),

  /**
   * Pin a channel for the current user
   */
  pinChannel: employedProcedure
    .input(z.object({
      channelId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      // Verify the user has access to this channel by checking if they're a member
      const channels = await ctx.streamChatClient.queryChannels({
        id: input.channelId,
        members: {$in: [ctx.currentPersonId]}
      });

      if (isEmpty(channels)) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Channel not found or you don't have access to it"
        });
      }

      // Create or update the pinned channel record
      const result = await ctx.prisma.pinnedChannel.upsert({
        where: {
          personId_channelId: {
            personId: ctx.currentPersonId,
            channelId: input.channelId
          }
        },
        update: {
          pinnedAt: new Date()
        },
        create: {
          personId: ctx.currentPersonId,
          channelId: input.channelId
        }
      });
    }),

  /**
   * Unpin a channel for the current user
   */
  unpinChannel: employedProcedure
    .input(z.object({
      channelId: z.string(),
    }))
    .output(z.void())
    .mutation(async ({ctx, input}) => {
      const result = await ctx.prisma.pinnedChannel.deleteMany({
        where: {
          personId: ctx.currentPersonId,
          channelId: input.channelId
        }
      });
    }),

  /**
   * Get pinned channels for the current user (across all stores)
   */
  getPinnedChannels: employedProcedure
    .input(z.object({})) // No storeId needed
    .output(z.object({
      pinnedChannelIds: z.array(z.string())
    }))
    .query(async ({ctx, input}) => {
      const pinnedChannels = await ctx.prisma.pinnedChannel.findMany({
        where: {
          personId: ctx.currentPersonId,
          // No storeId filter - get all pinned channels for this user
        },
        select: {
          channelId: true
        },
        orderBy: {
          pinnedAt: 'desc'
        }
      });

      // Get existing channels from Stream Chat to filter out deleted ones
      const channelIds = map(pinnedChannels, 'channelId');
      if (isEmpty(channelIds)) {
        return { pinnedChannelIds: [] };
      }


      // Query all channels the user has access to, then filter for pinned ones
      const allUserChannels = await ctx.streamChatClient.queryChannels({
        members: {$in: [ctx.currentPersonId]}
      }, {}, {
        limit: 1000
      });


      // Filter to only include channels that are in our pinned list
      const existingChannels = filter(allUserChannels, channel =>
        includes(channelIds, channel.id!)
      );

      const existingChannelIds = map(existingChannels, 'id');

      // Clean up any pinned channels that no longer exist (after trying both batch and individual queries)
      const deletedChannelIds = filter(channelIds, id => !includes(existingChannelIds, id));
      if (!isEmpty(deletedChannelIds)) {
        await ctx.prisma.pinnedChannel.deleteMany({
          where: {
            personId: ctx.currentPersonId,
            channelId: {in: deletedChannelIds}
          }
        });
      }

      return {
        pinnedChannelIds: existingChannelIds
      };
    })
});
